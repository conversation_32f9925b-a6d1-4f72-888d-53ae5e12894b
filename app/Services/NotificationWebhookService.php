<?php

namespace App\Services;

use App\Jobs\ProcessNotificationWebhook;
use Illuminate\Auth\Notifications\ResetPassword;
use Illuminate\Mail\Mailable;
use Illuminate\Notifications\Notification as BaseNotification;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification as NotificationFacade;
use Illuminate\Support\Facades\Password;
use InvalidArgumentException;
use Webkul\Customer\Models\Customer;

class NotificationWebhookService
{
    public function processWebhook(array $payload): array
    {
        $alias = $payload['class'];
        $map   = config('notification-webhook.class_aliases', []);

        if (! isset($map[$alias])) {
            throw new InvalidArgumentException("Unknown webhook class alias [{$alias}].");
        }

        dispatch(new ProcessNotificationWebhook($payload['class'], $payload['data']))->onQueue('launchpad');

        return [
            'status'  => 'queued',
            'message' => "Webhook [{$alias}] dispatched to queue.",
        ];
    }

    public function sendByAlias(string $alias, array $payloadData): void
    {
        $map = config('notification-webhook.class_aliases', []);
        if (! isset($map[$alias])) {
            throw new InvalidArgumentException("Unknown webhook class alias [{$alias}].");
        }

        $class = $map[$alias];

        if ($alias === 'UpdatePassword') {
            $this->handleCustomerUpdatePasswordMail($class, $payloadData);

            return;
        }

        if ($alias === 'ResetPassword') {
            $this->handleResetPasswordNotification($class, $payloadData);

            return;
        }

        if (is_subclass_of($class, Mailable::class)) {
            $mailable = new $class($payloadData);
            Mail::queue($mailable);

            return;
        }

        if (is_subclass_of($class, BaseNotification::class)) {
            $this->sendNotification($class, $payloadData);

            return;
        }

        throw new InvalidArgumentException(
            "Class [{$class}] is neither a Mailable nor a Notification."
        );
    }

    protected function handleCustomerUpdatePasswordMail($mailClass, array $payloadData): void
    {
        $customer = Customer::where('email', $payloadData['email'])->first() ?? throw new InvalidArgumentException("Customer not found for email {$payloadData['email']}.");

        $mailable = new $mailClass($customer);

        Mail::queue($mailable);
    }

    protected function handleResetPasswordNotification(string $notificationClass, array $payloadData): void
    {
        $email = $payloadData['data']['email']
            ?? throw new InvalidArgumentException("Missing 'email' for {$notificationClass}.");

        $customer = Customer::where('email', $email)->first()
            ?? throw new InvalidArgumentException("Customer not found for email {$email}.");

        $token = Password::broker('customers')->createToken($customer);

        ResetPassword::createUrlUsing(function ($notifiable, $token) {
            return route('customer.reset-password.create', $token);
        });

        $notification = new $notificationClass($token, $customer);

        $customer->notify($notification);
    }

    protected function sendNotification(string $class, array $payloadData): void
    {
        $email      = $payloadData['email'] ?? throw new InvalidArgumentException("Missing 'email' for Notification [{$class}].");
        $notifiable = Customer::where('email', $email)->first()
            ?? throw new InvalidArgumentException("Customer not found for email {$email}.");

        $params       = $payloadData['data'] ?? [];
        $notification = new $class(...$params);

        NotificationFacade::send($notifiable, $notification);
    }
}
