<?php

namespace App\Jobs;

use App\Services\NotificationWebhookService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ProcessNotificationWebhook implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 5;

    public array $backoff = [15, 30, 60, 120, 240];

    public int $timeout = 300;

    protected string $alias;

    protected array $data;

    public function __construct(string $alias, array $data)
    {
        $this->alias = $alias;
        $this->data  = $data;
    }

    public function handle(NotificationWebhookService $service): void
    {
        $service->sendByAlias($this->alias, $this->data);
    }
}
