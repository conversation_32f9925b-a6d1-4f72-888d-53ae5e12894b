@extends('shop::layouts.miligold-default')

@inject ('productRatingHelper', 'Webkul\Product\Helpers\Review')

@php
    $channel = core()->getCurrentChannel();

    $homeSEO = $channel->home_seo;

    if (isset($homeSEO)) {
        $homeSEO = json_decode($channel->home_seo);

        $metaTitle = $homeSEO->meta_title;

        $metaDescription = $homeSEO->meta_description;

        $metaKeywords = $homeSEO->meta_keywords;
    }
@endphp

@section('page_title')
    {{ isset($metaTitle) ? $metaTitle : "" }}
@endsection

@section('head')
    @if (isset($homeSEO))
        @isset($metaTitle)
            <meta name="title" content="{{ $metaTitle }}"/>
        @endisset

        @isset($metaDescription)
            <meta name="description" content="{{ $metaDescription }}"/>
        @endisset

        @isset($metaKeywords)
            <meta name="keywords" content="{{ $metaKeywords }}"/>
        @endisset
    @endif
@endsection

@section('body')
    <main class="pt-[5.5rem] lg:pt-24">
        <section class="relative py-24 dark:bg-jacarta-800">
            <picture class="pointer-events-none absolute inset-0 -z-10 hidden dark:block">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/gradient-inpage.webp" type="image/webp">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" type="image/png">
                <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" class="h-full w-full">
            </picture>
            <picture class="pointer-events-none absolute inset-0 -z-10 dark:hidden">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/gradient-inpage.webp" type="image/webp">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" type="image/png">
                <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png" class="h-full w-full">
            </picture>
            <div class="container">
                <div class="pb-10">
                    <!-- Info -->
                    <div class=" w-full 2xl:pt-20 md:pt-16">
                        <h2 class="mb-6 font-display text-3xl text-jacarta-700 dark:text-white">
                            {!! __('velocity::app-gold.whats_milyem_gold') !!}
                        </h2>
                        <picture>
{{--                            <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/coin_milyem.webp" type="image/webp">--}}
                            <source srcset="/miligold/img/miligram-coin.png" type="image/png">
                            <img src="/miligold/img/miligram-coin.png" alt="Milyem Coin" class="float-right max-w-[200px] lg:max-w-[400px] mb-4 pl-4 transition-transform duration-500 animate-reverse-spin-slow">
                        </picture>

                        <p class="text-lg text-jacarta-500 mb-4 text-justify">
                            {!! __('velocity::app-gold.whats_milyem_gold_description_1') !!}
                        </p>
                        <p class="text-lg text-jacarta-500 mb-4 text-justify">
                            {!! __('velocity::app-gold.whats_milyem_gold_description_2') !!}
                        </p>
                    </div>

                    <div class="w-full">
                        <p class="text-lg text-jacarta-500 mb-4 text-justify">
                            {!! __('velocity::app-gold.whats_milyem_gold_description_3') !!}
                        </p>
                        <p class="text-lg text-jacarta-500 mb-4 text-justify">
                            {!! __('velocity::app-gold.whats_milyem_gold_description_4') !!}
                        </p>
                        <p class="text-lg text-jacarta-500 mb-4 text-justify">
                            {!! __('velocity::app-gold.whats_milyem_gold_description_5') !!}
                        </p>
                        <p class="text-lg text-jacarta-500 mb-4 text-justify">
                            {!! __('velocity::app-gold.whats_milyem_gold_description_6') !!}
                        </p>
                        <p class="text-lg text-jacarta-500 mb-4 text-justify">
                            {!! __('velocity::app-gold.whats_milyem_gold_description_7') !!}
                        </p>
                    </div>
                    <div class="lg:flex lg:justify-between mt-8">
                        <!-- Image -->
                        <div class="relative pr-6 lg:w-[20%]">
                            <picture>
{{--                                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/milyem.webp" type="image/webp">--}}
                                <source srcset="/miligold/img/mili-ikon.png" type="image/png">
                                <img src="/miligold/img/mili-ikon.png" alt="MiliGold" class="w-full max-w-[200px] ml-auto" loading="lazy">
                            </picture>
                        </div>

                        <!-- Info -->
                        <div class="py-10 px-6 lg:w-[80%] lg:pl-12 flex flex-col justify-center items-start">
                            <h2 class="mb-6 font-display text-3xl text-[#000000] dark:text-white">
                                {!! __('velocity::app-gold.milyem_story_title') !!}
                            </h2>
                            <p class="mb-12 text-lg leading-normal text-jacarta-500 dark:text-jacarta-300 italic">
                                {!! __('velocity::app-gold.milyem_story_description') !!}
                            </p>

                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
@endsection
@push('scripts')
    <script src="{{ asset('js/app.js') }}"></script>
@endpush

