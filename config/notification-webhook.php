<?php

return [
    'class_aliases'   => [
        'Registration'    => Webkul\Customer\Mail\RegistrationEmail::class,
        'ResetPassword'   => Webkul\Customer\Notifications\CustomerResetPassword::class,
        'UpdatePassword'  => Webkul\Customer\Notifications\CustomerUpdatePassword::class,
    ],
    'class_validators' => [
        'Registration' => [
            'data.data.email'      => 'required|email|exists:customers,email',
            'data.data.first_name' => 'required|string',
            'data.data.last_name'  => 'required|string',
            'data.mailType'        => 'required|string|in:customer,admin',
        ],
        'ResetPassword' => [
            'data.data.email' => 'required|email|exists:customers,email',
        ],
        'UpdatePassword' => [
            'data.email' => 'required|email|exists:customers,email',
        ],
    ],

    // SHA1: THORNE-IT-NOTIFICATION-WEBHOOK-TOKEN
    'webhook_token' => env('NOTIFICATION_WEBHOOK_TOKEN'),
];
