<?php

use Illuminate\Support\Facades\Route;
use Webkul\CMS\Http\Controllers\Shop\PagePresenterController;
use Webkul\Shop\Http\Controllers\CategoryController;
use Webkul\Shop\Http\Controllers\HomeController;
use Webkul\Shop\Http\Controllers\ProductController;
use Webkul\Shop\Http\Controllers\ReviewController;
use Webkul\Shop\Http\Controllers\SearchController;

Route::group(['middleware' => ['web', 'locale', 'theme', 'currency']], function () {
    /**
     * Cart merger middleware. This middleware will take care of the items
     * which are deactivated at the time of buy now functionality. If somehow
     * user redirects without completing the checkout then this will merge
     * full cart.
     *
     * If some routes are not able to merge the cart, then place the route in this
     * group.
     */
    Route::group(['middleware' => ['cart.merger']], function () {
        /**
         * CMS pages.
         */
        Route::get('page/{slug}', [PagePresenterController::class, 'presenter'])->name('shop.cms.page');

        /**
         * Fallback route.
         */
        Route::fallback(\Webkul\Shop\Http\Controllers\ProductsCategoriesProxyController::class.'@index')
            ->defaults('_config', [
                'product_view'  => 'shop::products.view',
                'category_view' => 'shop::products.index',
            ])
            ->name('shop.productOrCategory.index');

        Route::get('all', [\Webkul\Shop\Http\Controllers\ProductsCategoriesProxyController::class, 'allProducts'])
            ->defaults('_config', [
                'store_view'  => 'shop::products.store',
            ])
            ->name('shop.productOrCategory.all');
    });

    /**
     * Store front home.
     */
    Route::get('/', [HomeController::class, 'index'])->defaults('_config', [
        'view' => 'shop::home.index',
    ])->name('shop.home.index');

    Route::get('/pages/{slug}', [HomeController::class, 'detailPage'])->defaults('_config', [
        'view' => 'shop::home.detail-page',
    ])->name('shop.home.detail-page');

    Route::get('/milyem-nedir', [HomeController::class, 'whatsMilyem'])->defaults('_config', [
        'view' => 'shop::home.whatsMilyem',
    ])->name('shop.home.whats-milyem');
    Route::get('/miligram-nedir', [HomeController::class, 'whatsMilyem'])->defaults('_config', [
        'view' => 'shop::home.whatsMilyem',
    ])->name('shop.home.whats-milyem');

    Route::get('/about-us', [HomeController::class, 'aboutUs'])->defaults('_config', [
        'view' => 'shop::home.about-us',
    ])->name('shop.home.about-us');

    Route::get('/about-us', [HomeController::class, 'aboutUs'])->defaults('_config', [
        'view' => 'shop::home.about-us',
    ])->name('shop.home.about-us');

    Route::get('/our-vision', [HomeController::class, 'ourVision'])->defaults('_config', [
        'view' => 'shop::home.our-vision',
    ])->name('shop.home.our-vision');

    Route::get('/faq', [HomeController::class, 'faq'])->defaults('_config', [
        'view' => 'shop::home.faq',
    ])->name('shop.home.faq');

    Route::get('/contact-us', [HomeController::class, 'contactUs'])->defaults('_config', [
        'view' => 'shop::home.contact-us',
    ])->name('shop.home.contact-us');

    Route::post('/contact-us', [HomeController::class, 'contactUsSubmit'])->defaults('_config', [
        'view' => 'shop::home.contact-us',
    ])->name('shop.home.contact-us.submit');

    Route::get('/neden-terramirum', [HomeController::class, 'mailingPage'])->defaults('_config', [
        'view' => 'shop::home.mailing',
    ])->name('shop.home.mailing');

    /**
     * Store front search.
     */
    Route::get('/search', [SearchController::class, 'index'])->defaults('_config', [
        'view' => 'shop::search.search',
    ])->name('shop.search.index');

    Route::post('/upload-search-image', [HomeController::class, 'upload'])->name('shop.image.search.upload');

    /**
     * Subscription routes.
     */
    Route::get('/subscribe/{token}', [\App\Http\Controllers\HomeController::class, 'Subscribe'])->name('shop.subscribe');

    Route::get('/unsubscribe/{token}', [\App\Http\Controllers\HomeController::class, 'Unsubscribe'])->name('shop.unsubscribe');

    /**
     * Product and categories routes.
     */
    Route::get('/reviews/{slug}', [ReviewController::class, 'show'])->defaults('_config', [
        'view' => 'shop::products.reviews.index',
    ])->name('shop.reviews.index');

    Route::get('/product/{slug}/review', [ReviewController::class, 'create'])->defaults('_config', [
        'view' => 'shop::products.reviews.create',
    ])->name('shop.reviews.create');

    Route::post('/product/{slug}/review', [ReviewController::class, 'store'])->defaults('_config', [
        'redirect' => 'shop.home.index',
    ])->name('shop.reviews.store');

    Route::get('/downloadable/download-sample/{type}/{id}', [ProductController::class, 'downloadSample'])->name('shop.downloadable.download_sample');

    Route::get('/product/{id}/{attribute_id}', [ProductController::class, 'download'])->defaults('_config', [
        'view' => 'shop.products.index',
    ])->name('shop.product.file.download');

    Route::get('categories/filterable-attributes/{categoryId?}', [CategoryController::class, 'getFilterAttributes'])->name('catalog.categories.filterable-attributes');
    Route::get('categories/all', [CategoryController::class, 'getAllCategories'])->name('catalog.categories.index');
    Route::get('categories/maximum-price/{categoryId?}', [CategoryController::class, 'getCategoryProductMaximumPrice'])->name('catalog.categories.maximum-price');
});
