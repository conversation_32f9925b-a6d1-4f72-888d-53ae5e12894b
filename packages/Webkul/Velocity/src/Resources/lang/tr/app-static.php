<?php

return [
    'congratulations' => 'Tebrikler',
    'homepage'        => [
        'download'             => 'İndir',
        'drsp'                 => 'Dijital Gayrimenkul His<PERSON>edi (DGHS)',
        'search-text'          => 'Ara...',
        'content'              => '<PERSON><PERSON><PERSON><PERSON>in her bölgesinden gayrimenkul yatırımcıları, hızl<PERSON>, gü<PERSON><PERSON>, kullanıcı dostu ve 7/24 erişilebilir olan yatırım platformumuzu çok sevecek.',
        'content-line'         => '<PERSON><PERSON> ekibimizle her zaman yanınızdayız!',
        'easy-buy-sell'        => 'Kolay Alım Satım',
        'buy-sell'             => 'Al/Sat',
        'drsp-card'            => 'Dijital gayrimenkul hisse senetleri kullanarak gayrimenkul mülklerinizi anında alabilir veya satabilirsiniz.',
        'mfnft-title'          => 'Çok Parçalı Değiştirilemez Token/ÇPDT',
        'mfnft-card'           => 'Token sahipleri, gayrimenkul mülklerinden elde edilen kira geliri ve diğer gelirlerde mülkiyet haklarına sahiptir. Ayrıca oy haklarını kullanarak gayrimenkulle ilgili topluluk kararlarına katılabilirler.',
        'nft-title'            => 'NFT (Değiştirilemez Token) Belgeleme',
        'nft-card'             => 'Gayrimenkul mülklerinizi NFT (Değiştirilemez Token) olarak basabilir ve ulusal ve uluslararası pazarlarda nakde çevirebilirsiniz.',
        'ensure'               => 'Bunu Nasıl Başarıyoruz?',
        'ensure-comment'       => 'Thorne, blokzincir teknolojisini kullanarak daha hızlı, daha güvenli ve kârlı gayrimenkul yatırımları yapmanızı sağlar.',
        'ensure-text'          => 'Blokzincir, NFT, Web 3, DeFi gibi terimlerle karşılaştığınız bu yeni teknolojik dünyada size rehberlik etmeyi amaçlıyoruz.',
        'text'                 => 'Gayrimenkul mülklerinden alım yapabilir, satabilir ve kira geliri elde edebilirsiniz.',
        'start-button'         => 'Başlayın',
        'portfolio'            => 'Portföyümüzdeki ürünlerin dijital gayrimenkul hisse senetlerini (DGHS) alıp satanlardan biri olun!',
        'terramirum-text'      => 'Pazar yerimiz (TerraMirum), yaklaşık 3 trilyon USD işlem hacmi ile sanal dünya yatırımcılarına blokzincir teknolojisine dayalı modern bir yatırım alanı olarak sunuluyor ve onları gerçek dünyadan kopmamaya teşvik ediyor.',
        'tenant'               => 'Kiracı',
        'thorne'               => 'Thorne',
        'income'               => 'Gelir',
        'convert'              => 'Tokena Dönüştürür',
        'token'                => 'Token Sahiplerine Dağıtılır',
        'send-bank'            => 'Banka Hesabınıza Gönderir',
        'slider-text'          => 'Blokzincir Tabanlı Gayrimenkul',
        'slider-text-line'     => 'Yatırım Platformu',
        'subscribe-newsletter' => 'Bültenimize abone olun',
        'sign-up'              => 'Kayıt Ol',
        'kvvk-agree-text'      => 'Gizlilik Beyanını ve KVKK Politikasını kabul ediyorum',
        'subscribe-ok'         => 'E-posta bülten listesine eklendi',
        'check-email'          => 'E-posta adresinizi kontrol edin!',
        'check-kvkk'           => 'Sözleşmeleri onayladığınızdan emin olun!',
        'contact'              => [
            'submit-success' => 'Mesajınız başarılı bir şekilde gönderildi.',
            'submit-error'   => 'Mesajınız gönderilemedi.',
        ],
    ],

    'subscriber' => [
        'subscribe'       => 'Bültenimize abone olun',
        'kvkk'            => 'Gizlilik Beyanını ve KVKK Politikasını kabul ediyorum',
        'subscriber-text' => 'Thorne Bilişim A.Ş., "Model Dijital Ürün" seçimiyle başlayarak gayrimenkul sektöründe dijital çözüm önerileri geliştirmeyi hedeflemektedir.',
    ],

    'products' => [
        'product-cat-title'   => 'Kategorideki en lüks ilanlar',
        'product-cat-text'    => 'En çok puan alan 3. ilan',
        'breadcrumb-part1'    => 'Ana Sayfa',
        'breadcrumb-part2'    => 'Konut',
        'breadcrumb-land'     => 'Arsa',
        'unit-price'          => 'm² Fiyatı',
        'unit-price-land'     => 'Birim Fiyatı',
        'value'               => 'Değer',
        'drsp-sales'          => 'DGHS Satış Oranı',
        'drsp-unit-price'     => 'DGHS Hisse Birim Fiyatı',
        'property-value'      => 'Gayrimenkul Değeri',
        'drsp-total'          => 'Toplam DGHS Hisse Miktarı',
        'using'               => 'Konut Kullanımı',
        'type'                => 'Bina/Arsa Türü',
        'usage-area'          => 'Kullanım Alanı',
        'using-status'        => 'Kullanım Durumu',
        'details'             => 'Detaylar',
        'bath-color'          => 'Banyo Rengi',
        'kitchen-color'       => 'Mutfak Rengi',
        'room'                => 'Oda',
        'usage-area-sqfit'    => 'Konut Kullanımı / Metrekare',
        'parking'             => 'Otopark',
        'price'               => 'Fiyat',
        'land-city'           => 'Şehir',
        'land-district'       => 'İlçe',
        'land-neighborhood'   => 'Mahalle',
        'land-ada'            => 'Ada',
        'land-parcel'         => 'Parsel',
        'land-expertise'      => 'Uzmanlık',
        'land-area'           => 'Alan',
        'land-kind'           => 'Tür',
        'land-status'         => 'Mevcut Durum',
        'land-unit-price'     => 'Birim Fiyat',
        'land-total-price'    => 'Toplam Fiyat',
        'land-total-nft'      => 'Toplam NFT',
        'land-nft-unit-price' => 'NFT Birim Fiyatı',
        'land-sold-nft'       => 'Satılan NFT',
        'land-sale-nft'       => 'Satıştaki NFT',
        'land-increase'       => 'NFT Değer Artışı',
    ],

    'cart' => [
        'page-title'      => 'Sepet',
        'cart-text'       => 'Sepetim',
        'empty-msg'       => 'Sepetiniz Boş',
        'empty-btn-msg'   => 'İncelemeye Devam Et',
        'share-amount'    => 'Miktar',
        'total-amount'    => 'Toplam (Hisse) Miktarı',
        'remainig-share'  => 'Birim Miktarı',
        'address'         => 'Faturam aynı adrese gelecek.',
        'continue'        => 'Devam Et',
        'payment-methods' => 'Ödeme yöntemleri',
        'payment-options' => 'Ödeme seçenekleri',
        'address-info'    => 'Adres bilgisi',
        'share-info'      => 'Hisse Bilgisi',
        'campaign-info'   => 'içerisinde indirim uygulandı',
        'discount'        => 'İndirim',
        'discount-amount' => 'İndirimli Tutar',
    ],

    'mini-cart' => [
        'total' => 'Toplam',
        'cart'  => 'Sepet',
        'share' => 'Hisse Sayısı',
        'pay'   => 'Öde',
    ],

    'login' => [
        'remember'             => 'Beni Hatırla',
        'continue'             => 'Üye olmadan devam et',
        'signup'               => 'Kayıt Ol',
        'signin'               => 'TerraMirum hesabınıza giriş yapın',
        'page-title'           => 'Giriş Sayfası',
        'singin-button'        => 'Giriş Yap',
        'forgot-your-password' => 'Şifrenizi mi unuttunuz?',
        'email-required'       => 'E-posta gerekli.',
        'password-placeholder' => 'Şifre',
        'email-placeholder'    => 'E-posta',
        'email-error-text'     => 'Lütfen doğru E-posta girdiğinizden emin olun.',
        'password-error-text'  => 'Lütfen doğru şifre girdiğinizden emin olun.',
    ],

    'signup' => [
        'hello'           => 'Merhaba',
        'start-msg'       => 'Bir hesap oluşturun, fırsatı kaçırmayın!',
        'terms'           => '"Kaydol" u tıklayarak üyelik şartlarını kabul ediyorum.',
        'id'              => 'Kimlik Numarası',
        'verify'          => 'Doğrula',
        'phone'           => 'Telefon',
        'pass-confirm'    => 'Şifreyi Onayla',
        'uname'           => 'Kullanıcı Adı',
        'btn-text'        => 'Kaydol',
        'nationality'     => 'Uyruk',
        'for-personal'    => 'Bireysel Üyelik',
        'for-instutional' => 'Kurumsal Üyelik',
        'personal-info'   => 'Yetkili Kullanıcı bilgileri',
        'company-info'    => 'Şirket bilgileri',
        'place-of-birth'  => 'Doğum Yeri',
        'personal-wallet' => 'Cüzdan Adresi (Zorunlu değil)',
    ],
    'forgot-password' => [
        'page-title'        => 'Şifremi Unuttum',
        'section-title'     => 'Giriş yapmakta sorun mu yaşıyorsunuz?',
        'email'             => 'E-posta',
        'remember-password' => 'Şifrenizi hatırladınız mı?',
        'submit-btn'        => 'Sıfırlama Maili Gönder',
    ],
    'reset-password' => [
        'page-title'        => 'Şifre Sıfırlama',
        'section-title'     => 'Giriş yapmakta sorun mu yaşıyorsunuz?',
        'email'             => 'E-posta',
        'new-password'      => 'Yeni Şifre',
        'confirm-password'  => 'Yeni Şifreyi Onayla',
        'remember-password' => 'Şifrenizi hatırladınız mı?',
        'submit-btn'        => 'Şifremi Sıfırla',
    ],
    'updated-password' => [
        'page-title'    => 'Başarılı',
        'section-title' => 'Şifreniz başarıyla sıfırlandı.',
        'login-btn'     => 'Giriş Yap',
    ],
    'verification' => [
        'page-title' => 'Doğrulama',
        'hello'      => 'Merhaba',
        'start-msg'  => 'Devam etmek için lütfen hesabınızı doğrulayın.',

        'email' => [
            'required' => 'Geçerli E-posta adresi girin',
            'email'    => 'E-posta geçerli değil',
            'unique'   => 'E-posta zaten alınmış',
        ],
        'id_number' => [
            'required' => 'Geçerli kimlik numarası girin',
            'numeric'  => 'Kimlik Numarası sayısal olmalı',
            'digits'   => 'Kimlik Numarası 11 haneli olmalı',
            'unique'   => 'Kimlik Numarası zaten alınmış',
        ],
        'first_name' => [
            'required' => 'İsim alanı zorunlu',
            'string'   => 'Ad bir metin olmalı',
        ],
        'last_name' => [
            'required' => 'Soyad alanı zorunlu',
            'string'   => 'Soyad bir metin olmalı',
        ],
        'date_of_birth' => [
            'required'    => 'Doğum Tarihi alanı zorunlu',
            'date_format' => 'Doğum Tarihi YYYY-AA-GG formatında olmalı',
        ],
        'phone' => [
            'required' => 'Geçerli telefon numarası girin',
        ],
        'user_name' => [
            'required' => 'Geçerli kullanıcı adı girin',
        ],
    ],
    'verification-pending' => [
        'page-title'  => 'Doğrulama',
        'hello'       => 'Merhaba',
        'start-msg'   => 'Doğrulama işlemi devam ediyor. Lütfen daha sonra tekrar deneyin.',
        'contact-msg' => 'Doğrulamanız devam ediyor. Yardıma mı ihtiyacınız var? Bizimle iletişime geçin.',
        'verify-info' => 'Hesabınızın doğrulama süreci tamamlanmadı, ancak alışveriş yapabilirsiniz. Çekimler için doğrulama tamamlanmalıdır.',
    ],
    'category' => [
        'price-range'    => 'Fiyat Aralığı',
        'all-categories' => 'Tüm Kategoriler',
    ],
    'wishlist' => [
        'title'         => 'Dilek Listesi',
        'empty-msg'     => 'Dilek listenizde ürün bulunmamaktadır.',
        'empty-content' => 'Binlerce paya bölünmüş özel emlak tokenlerini keşfedin.',
        'btn-text'      => 'Keşfet',
    ],
    'address' => [
        'empty-msg'     => 'Profilinizde adres bulunmamaktadır.',
        'empty-content' => 'Burada kaydedilmiş herhangi bir adresiniz bulunmamaktadır, lütfen eklemek için ekle düğmesine tıklayarak deneyin.',
    ],

    'user-profile' => [
        'wallet'             => 'Cüzdan/Siparişler',
        'wallet-address'     => 'Cüzdan Adresi',
        'empty-wallet'       => 'Cüzdanınız boş',
        'table-product-name' => 'Ürün',
        'table-actions'      => 'İşlemler',
        'contract-button'    => 'Akıllı Kontrat',
        'contract-notfound'  => 'Akıllı Kontrat Bulunamadı',
        'advantage'          => 'Avantajlar',
        'wishlist'           => 'Dilek Listesi',
        'notification'       => 'Bildirimler',
        'address'            => 'Adreslerim',
        'logout'             => 'Çıkış',
        'preferences'        => 'İletişim Tercihleri',
        'user-info'          => 'Kullanıcı Bilgileri',
        'account-info'       => 'Hesap Bilgileri',
        'profile'            => 'Profil Bilgisi',
        'info'               => 'Kullanıcı adınızı en fazla 2 kez değiştirebilirsiniz.',
        'change'             => 'Değiştir',
        'pass-change'        => 'Şifre Değiştirme',
        'pass-change-msg'    => 'Şifreniz en az bir harf, rakam veya özel karakter içermelidir. Ayrıca, şifreniz en az 8 karakter olmalıdır.',
        'info-msg'           => 'Onay Metni kapsamında, önemli kampanyalar hakkında bilgilendirilmek istediğiniz yöntemleri belirtebilirsiniz.',
        'info-email'         => 'Beni ilgilendirebilecek kampanyalar ve piyasa bültenleri hakkında e-posta almak istiyorum.',
        'info-sms'           => 'Beni ilgilendirebilecek kampanyalar ve piyasa bültenleri hakkında SMS almak istiyorum.',
        'info-tel'           => 'Beni ilgilendirebilecek kampanyalar ve piyasa bültenleri hakkında arama almak istiyorum.',
        'info-text'          => 'Kampanyalarla ilgili iletişim tercihlerinizi kapatmanız durumunda, üyelik ayarlarınıza ilişkin e-posta, bildirim, SMS veya telefon araması almaya devam edebilirsiniz.',
        'info-part'          => 'Onay Metni kapsamında, önemli kampanyalar hakkında bilgilendirilmek istediğiniz yöntemleri belirtebilirsiniz.',
        'consent-text'       => 'Onay Metni',
        'amount-vue'         => 'Miktar',
        'email'              => 'E-posta',
        'sms'                => 'SMS',
        'call'               => 'Telefon Görüşmesi',
        'current-pass'       => 'Mevcut Şifre',
        'new-pass'           => 'Yeni Şifre',
        'confirm-pass'       => 'Yeni Şifre Tekrar',
        'wallet-tab'         => 'Cüzdan',
        'orders-tab'         => 'Siparişlerim',
        'nft-tab'            => 'NFT\'lerim',
        'trans-tab'          => 'İşlemler',
        'orders-menu-title'  => 'Cüzdan/Siparişler',
        'wallet-currency'    => 'Birim',
        'wallet-amount'      => 'Miktar',
        'nft-empty-msg'      => 'Binlerce hisseye bölünmüş özel gayrimenkul ve arazi tokenlerini keşfedin.',
        'nft-empty-title'    => 'Henüz bir NFT\'niz yok.',
        'transactions'       => 'İşlemler',
        'deposit'            => 'Yükle',
        'withdraw'           => 'Çek',
        'balance'            => 'Bakiye',
        'locked-balance'     => 'Bekleyen Bakiye',
    ],

    'orders' => [
        'title'              => 'Siparişiniz alındı, Tebrikler.',
        'btn-text'           => 'Satın alımlarıma git',
        'content'            => 'Siparişinizle ilgili detaylar gönderildi',
        'order-no'           => 'Sipariş Numarası',
        'billing'            => 'Fatura Adresi',
        'amount'             => 'Ödenen Miktar',
        'bank-info'          => 'Banka Hesap Bilgileri',
        'bank-name'          => 'Banka',
        'account-name'       => 'Hesap Adı',
        'iban'               => 'IBAN',
        'branch-code'        => 'Şube Kodu',
        'success'            => 'Siparişiniz başarıyla oluşturuldu.',
        'success-for-crypto' => 'Siparişiniz başarıyla oluşturuldu.',
        'thanks'             => '<p class="p-5 pt-2 fs-5 lh-base text-center"><span class="text-warning">MiliGOLD ailesine katıldığınız için teşekkür ederiz</span> <br>Siparişiniz başarıyla oluşturuldu. <br>Sipariş detaylarınıza <a href="/customer/account/orders">Siparişlerim</a> sayfasından erişebilir ve durumunu kontrol edebilirsiniz.<br>Aşağıda yer alan hesap bilgilerine sipariş tutarı kadar ödemeyi <a style="color:#ff9700;">sipariş numarası ile birlikte</a> yaptıktan ve<span style="color:#ff9700;">KYC</span> süreciniz tamamlandıktan sonra cüzdanınıza aktarım gerçekleşecektir.<br> Aktarım tamamlandıktan sonra sizinle <span style="color:#ff9700;">eposta</span> kanalıyla iletişime geçeceğiz.<br>Daha fazla bilgi için <a href="/pages/faq">SSS</a> bakabilirya da bizimle <a href="/contact-us">İletişim</a> kurabilirsiniz.</p>',
        'thanks-for-crypto'  => '<p class="p-5 pt-2 fs-5 lh-base text-center"><span class="text-warning">MiliGOLD ailesine katıldığınız için teşekkür ederiz</span> <br>Siparişiniz başarıyla oluşturuldu. <br>Sipariş detaylarınıza <a href="/customer/account/orders">Siparişlerim</a> sayfasından erişebilir ve durumunu kontrol edebilirsiniz.<br><span style="color:#ff9700;">KYC</span> süreciniz tamamlandıktan sonra cüzdanınıza aktarım gerçekleşecektir.<br> Aktarım tamamlandıktan sonra sizinle <span style="color:#ff9700;"><span style="color:#ff9700;">eposta</span> kanalıyla iletişime geçeceğiz.<br>Daha fazla bilgi için <a href="/pages/faq">SSS</a> bakabilirya da bizimle <a href="/contact-us">İletişim</a> kurabilirsiniz.</p>',
    ],

    'advantages' => [
        'title'           => 'Avantajlar',
        'coupons'         => 'Kuponlarım',
        'use'             => 'Kullan',
        'purchase-amount' => 'Satın Alma Tutarı',
        'conditions'      => 'Koşullar',
        'expire-date'     => 'Son Kullanma Tarihi',
        'last'            => 'Son',
        'days'            => 'günler',
        'campaigns'       => 'Kampanyalar',
        'discount'        => 'İndirim',
        'opportunity'     => 'Fırsat',
        'favorite'        => 'Favorilere Ekle',
        'notify'          => 'İlk Bilgi Almak İçin Sen Ol!',
        'add-fav'         => 'Favorilerinize ekleyin!',
        'expired'         => 'Süresi Dolmuş',
        'info-msg'        => 'Şimdi favorilere eklemeye başlayın, büyük indirim günlerindeki fırsatları ilk öğrenen siz olun',
        'empty-msg'       => 'Henüz tanımlanmış bir avantaj yok.',
    ],
    'profile' => [
        'index' => 'Hesap',
    ],
    // # ilave ##
    'profilePage' => [
        'profile'                                      => 'Profil',
        'user-name'                                    => 'Kullanıcı Adı',
        'last-name'                                    => 'Soyadı',
        'panel-button'                                 => 'Değiştir',
        'company-button'                               => 'Başvur',
        'my-profile'                                   => 'Profilim',
        'profile-orders'                               => 'Siparişler',
        'profile-wallet'                               => 'Cüzdan',
        'logout'                                       => 'Çıkış',
        'first-name'                                   => 'Adı',
        'profile-account'                              => 'Hesap',
        'user-phone'                                   => 'Telefon Numarası',
        'user-email'                                   => 'E-posta Adresi',
        'profile-security'                             => 'Güvenlik',
        'profile-page'                                 => 'Profil Sayfası',
        'new-password'                                 => 'Yeni Şifre',
        'profile-locked-coins'                         => 'Kilitli Tokenler',
        'current-password'                             => 'Mevcut Şifre',
        'confirm-password'                             => 'Şifreyi Onayla',
        'profile-transfer-details'                     => 'Transfer Detayları',
        'two-factor-authentication'                    => 'İki Faktörlü Kimlik Doğrulama',
        'google-two-factor-authentication-is-verified' => 'Google İki Faktörlü Kimlik Doğrulaması Doğrulandı',
        'institutional'                                => 'Şirketler için',
        'company-name'                                 => 'Şirket Adı',
        'company-mail'                                 => 'Şirket E-posta',
        'company-tax-number'                           => 'Şirket Kayıt Numarası',
        'company-phone'                                => 'Şirket Telefonu',
        'company-representative'                       => 'Şirket Yetkilisi',
        'company-address'                              => 'Şirket Adresi',
        'legal-form'                                   => 'Yasal Form',
        'company-name-error'                           => 'Şirket Adı Alanı Zorunludur.',
        'company-mail-error'                           => 'Şirket E-posta Alanı Zorunludur.',
        'company-tax-number-error'                     => 'Şirket Kayıt Numarası Alanı Zorunludur.',
        'company-phone-error'                          => 'Şirket Telefonu Alanı Zorunludur.',
        'company-representative-error'                 => 'Şirket Yetkilisi Alanı Zorunludur.',
        'company-address-error'                        => 'Şirket Adresi Alanı Zorunludur.',
        'legal-form-error'                             => 'Yasal Form Alanı Zorunludur.',
        'register-form'                                => 'Başvuru Formu',
        'error-message'                                => 'Lütfen aşağıda yer alan alanları doldurun',
        'personal-address'                             => 'Adres',
        'legal-type'                                   => 'Şirket Türü',
    ],
    // # ilave ##
    'walletPage' => [
        'wallet'                                                        => 'Cüzdan',
        'panel-balance'                                                 => 'Bakiye',
        'panel-currency'                                                => 'Zincir Para Birimi',
        'wallet-page'                                                   => 'Cüzdan Sayfası',
        'select-label-text'                                             => 'Zincir Seçin',
        'address-label-text'                                            => 'Cüzdan Adresi',
        'panel-pending-balance'                                         => 'İşlemde olan Bakiye',
        'panel-drop-button-deposit-text'                                => 'Yatır',
        'panel-drop-button-withdraw-text'                               => 'Çek',
        'panel-drop-menu-button-withcash-text'                          => 'Nakit Olarak Çek',
        'panel-drop-menu-button-withdraw-withcash-text'                 => 'Nakit Olarak Çek',
        'panel-drop-menu-button-deposit-withcash-text'                  => 'Nakit Para İle Yatır',
        'panel-drop-menu-button-withdraw-withcrypto-text'               => 'Kripto Olarak Çek',
        'panel-drop-menu-button-deposit-withcrypto-text'                => 'Kripto Para İle Yatır',
        'panel-modal-content-title-text'                                => 'Nakit Olarak Yatırma',
        'panel-drop-menu-button-withcrypto-text'                        => 'Kripto Para İle Yatır',
        'panel-modal-content-bank-account-information-iban-text'        => 'IBAN(TRY)',
        'panel-modal-content-bank-account-information-bankname-text'    => 'Banka Adı',
        'panel-modal-content-bank-account-information-text'             => 'Banka Hesap Bilgileri',
        'panel-modal-content-bank-account-information-accountname-text' => 'Hesap Adı',
        'panel-modal-content-body2-text'                                => '<p>Açıklama bölümüne <span><b>{accountId}</b></span> yazmalısınız</p>',
        'panel-modal-content-body1-text'                                => '<p>Bakiye yükleme işlemi için ilgili banka adreslerinden birine <span>Havale/EFT</span> yapmanız gerekmektedir.</p>',
        'panel-modal-crypto-content-title-text'                         => 'Kripto Para ile Yatırma',
        'panel-modal-crypto-content-h4-title-text'                      => 'Aşağıdaki Cüzdana Ödeme Yapmalısınız',
        'panel-modal-crypto-content-body-text'                          => '<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Fusce elit ligula, vulputate vel egestas id, scelerisque vitae risus. Vestibulum ut auctor arcu, ac ornare urna. Ut non felis tortor.</p>',
        'panel-modal-content-withdraw-title-text'                       => 'Nakit Para Çekme',
        'panel-modal-content-withdraw-body-text'                        => '<p>Lütfen giriş yaparken <span>Google İki Faktörlü Kimlik Doğrulama</span> anahtarını yazın</p>',
        'panel-modal-content-withdraw-error-title-text'                 => 'Lütfen aşağıdaki hata(ları) düzeltin:',
        'panel-modal-content-withdraw-button-text'                      => 'Çekme İşlemine Başla',
        'panel-modal-content-withdraw-body2-text'                       => '<p>Transfer için gerekli alanları doldurarak işlem yapabilirsiniz.</p><p><span>Sadece kendi hesabınıza transfer yapabilirsiniz</span></p>',
        'panel-modal-content-withdraw-input-amount-text'                => 'Miktar',
        'panel-modal-content-withdraw-textarea-text'                    => 'Notunuzu Yazın.',
        'panel-modal-content-withdrawcrypto-title-text'                 => 'Kripto Para Çekme',
        'panel-modal-toast-title'                                       => 'Başarılı',
        'panel-model-toast-body'                                        => 'Cüzdan Adresi başarıyla kopyalandı',
        'panel-modal-content-withdrawsuccess-title-text'                => 'Transfer işlemi başarılı.',
        'panel-modal-google2fa-button-text'                             => 'Değiştir',
        'panel-modal-validate-text'                                     => 'Lütfen zorunlu alanları doldurun.',
    ],
    // # ilave ##
    'lockedCoins' => [
        'locked-coins'            => 'Kilitli Tokenler',
        'panel-amount-text'       => 'Miktar',
        'panel-currency-text'     => 'Para Birimi',
        'panel-title-text'        => 'Serbest Bırakma',
        'select-coin-label-text'  => 'Coin Seçin',
        'locked-coins-page'       => 'Kilitli Tokenler Sayfası',
        'panel-release-date-text' => 'Serbest Bırakma Tarihi',
        'panel-empty-title'       => 'Kilitli Token Bulunamadı',
        'panel-empty-text'        => 'Şu anda kullanılabilir kilitli token bulunmamaktadır.',
    ],
    // # ilave ##
    'ordersPage' => [
        'orders'                            => 'Siparişler',
        'orders-page'                       => 'Siparişler Sayfası',
        'table-order-id'                    => 'Sipariş Kimliği',
        'table-order-date'                  => 'Sipariş Tarihi',
        'table-order-total'                 => 'Toplam',
        'table-order-pcs'                   => 'Adet',
        'table-order-status'                => 'Durum',
        'table-order-show'                  => 'Göster',
        'table-order-action'                => 'Aksiyon',
        'table-order-action-button-text'    => 'İptal et',
        'modal-content-title-text'          => 'Sipariş Edilen Ürünler',
        'modal-content-table-sku'           => 'COIN',
        'modal-content-table-name'          => 'Adı',
        'modal-content-table-pcs'           => 'Adet',
        'modal-content-table-price'         => 'Fiyat',
        'modal-content-table-total'         => 'Toplam',
        'modal-content-payment-method'      => 'Ödeme Yöntemi',
        'modal-content-payment-subtotal'    => 'Ara Toplam',
        'modal-content-payment-tax'         => 'Vergi',
        'modal-content-payment-grand-total' => 'Toplam Tutar',
        'modal-content-tx-id'               => 'TX ID',
        'modal-content-cancel-text'         => 'Siparişi İptal Etmek İstediğinizden Emin misiniz?',
        'modal-content-cancel-info-text'    => 'Blockchain üzerinden yapılan ödemeler, kalıcı şekilde bloklara işlendiği için İPTAL edilememektedir.',
        'modal-button-cancel-text'          => 'Vazgeç',
        'modal-button-success-text'         => 'Onayla',
    ],
    // # ilave ##
    'transferDetail' => [
        'transfer-detail-title-text'      => 'Transfer Detayları',
        'transfer-detail-page-title-text' => 'Transfer Detayları Sayfası',
        'button-today-text'               => 'Bugün',
        'button-thisweek-text'            => 'Bu Hafta',
        'button-thismonth-text'           => 'Bu Ay',
        'button-lastthreemonth-text'      => 'Son Üç Ay',
        'table-title'                     => 'Başlık',
        'table-currency'                  => 'Para Birimi',
        'table-balance'                   => 'Miktar',
        'table-blocked-balance'           => 'Bloke Edilen Bakiye',
        'table-date'                      => 'Tarih',
    ],

    'alert' => [
        'success' => 'Başarılı',
        'error'   => 'Hata',
        'warning' => 'Uyarı',
        'info'    => 'Bilgi',
    ],
    'checkout' => [
        'onepage' => [
            'page-title'                     => 'Ödeme',
            'heading'                        => 'Ödeme',
            'select-payment-type'            => 'Ödeme Türünü Seçin',
            'select-address'                 => 'Adres Seçin',
            'payment-method-money-transfer'  => 'Havale',
            'payment-method-pay-with-crypto' => 'Kripto ile Öde',
            'continue'                       => 'Devam Et',
            'cart'                           => 'Sepetim',
            'total'                          => 'Toplam Tutar',
            'update-cart'                    => 'Sepeti Güncelle',
        ],
    ],
    'payment' => [
        'summary' => [
            'check-order-question' => 'Faturanız siparişinize göre hazırlanacak ve <span class="text-terra-orange">e-posta</span> adresinize gönderilecektir. <br><br> Siparişinizi onaylıyor musunuz ?',
            'check-order'          => 'Siparişi Onayla',
            'cancel'               => 'İptal',
        ],
    ],
    'fast-order' => [
        'summary' => [
            'page-title'                 => 'Altın Al / Altın Sat',
            'title'                      => 'Altın Al / Sat',
            'subtitle'                   => 'İşlem türünü seçin ve güncel piyasa fiyatıyla almak veya satmak istediğiniz altın miktarını girin.',
            'buy-gold'                   => 'Altın Al',
            'sell-gold'                  => 'Altın Sat',
            'gold-purchase-confirmation' => 'Altın Alım Onayı',
            'gold-sale-confirmation'     => 'Altın Satım Onayı',
            'locked-price'               => 'KİLİTLİ FİYAT',
            'transaction-type'           => 'İşlem Türü:',
            'gold-amount'                => 'Altın Miktarı:',
            'unit-price'                 => 'Birim Fiyat:',
            'total-amount'               => 'Toplam Tutar:',
            'remaining-time'             => 'Kalan Süre:',
            'seconds'                    => 'saniye',
            'cancel'                     => 'İptal',
            'confirm'                    => 'Onayla',
            'price-locked-info'          => 'İşleminiz için geçerli fiyat bir süre sabitlenmiştir. Süre sonunda otomatik olarak güncel fiyatların görüntülendiği alım-satım ekranına yönlendirileceksiniz.',
            'gold-amount-label'          => 'Altın Miktarı',
            'money-amount-label'         => 'Para Miktarı',
            'enter-gold-amount'          => 'Altın miktarı girin',
            'enter-money-amount'         => 'Para miktarı girin',
            'add-balance'                => 'Bakiye Ekle',
            'live-rate'                  => 'CANLI KUR',
            'time-remaining'             => 'Kalan süre:',
            'rate-update-info'           => 'Süre sonunda kur bilgisi otomatik güncellenecektir.',
            'buy-limit-info'             => 'Satın alabileceğiniz altın tutarı <b>:amount :currency</b> tutarını geçemez. Daha fazlası için <a href=":route" class="text-[#D0AA49] font-bold">bakiye ekleyin</a>.',
            'sell-limit-info'            => 'Satabileceğiniz altın tutarı <b>:amount :currency</b> tutarını geçemez.',
        ],
        'result' => [
            'buy' => [
                'page-title'              => 'Sipariş Durumu',
                'order-success-title'     => 'Altın alımı başarıyla gerçekleşti!',
                'order-success-subtitle'  => 'Siparişiniz oluşturuldu, lütfen aşağıdaki adımları takip edin.',
                'order-created'           => 'Sipariş Oluşturuldu',
                'order-failed'            => 'Sipariş Başarısız',
                'order-failed-reason'     => 'Siparişiniz ile alakalı bir sorun oluştu. Lütfen destek ekibimiz ile iletişime geçin.',
                'order-failed-support'    => 'Destek ile İletişime Geçin',
                'completed'               => 'Tamamlandı',
                'payment-processing'      => 'Ödeme İşleniyor',
                'processing'              => 'İşleniyor',
                'balance-will-be-loaded'  => 'Bakiye Yüklenecek',
                'waiting'                 => 'Bekleniyor',
                'failed'                  => 'Hata Oluştu',
                'order-summary'           => 'Sipariş Özeti',
                'order-number'            => 'Sipariş Numarası:',
                'order-content'           => 'Sipariş İçeriği:',
                'order-amount'            => 'Sipariş Tutarı:',
                'order-status'            => 'Sipariş Durumu:',
                'order-status-pending'    => '<span class="text-yellow-500">Beklemede</span>',
                'order-status-processing' => '<span class="text-blue-500">İşleniyor</span>',
                'order-status-completed'  => '<span class="text-green-500">Tamamlandı</span>',
                'order-status-failed'     => '<span class="text-red-500">Başarısız</span>',
                'order-status-canceled'   => '<span class="text-red-500">İptal Edildi</span>',
                'order-date'              => 'Sipariş Tarihi:',
                'wallet'                  => 'Cüzdan',
                'wallet-after-completion' => '(Sipariş tamamlandıktan sonra)',
                'balance'                 => 'Bakiye:',
            ],
            'sell' => [
                'page-title'              => 'Sipariş Durumu',
                'order-success-title'     => 'Altın satışı başarıyla gerçekleşti!',
                'order-success-subtitle'  => 'Siparişiniz oluşturuldu, lütfen aşağıdaki adımları takip edin.',
                'order-created'           => 'Sipariş Oluşturuldu',
                'order-failed'            => 'Sipariş Başarısız',
                'order-failed-reason'     => 'Siparişiniz ile alakalı bir sorun oluştu. Lütfen destek ekibimiz ile iletişime geçin.',
                'order-failed-support'    => 'Destek ile İletişime Geçin',
                'completed'               => 'Tamamlandı',
                'payment-processing'      => 'Ödeme İşleniyor',
                'processing'              => 'İşleniyor',
                'balance-will-be-loaded'  => 'Bakiye Yüklenecek',
                'waiting'                 => 'Bekleniyor',
                'failed'                  => 'Hata Oluştu',
                'order-summary'           => 'Sipariş Özeti',
                'order-number'            => 'Sipariş Numarası:',
                'order-content'           => 'Sipariş İçeriği:',
                'order-amount'            => 'Sipariş Tutarı:',
                'order-status'            => 'Sipariş Durumu:',
                'order-status-pending'    => '<span class="text-yellow-500">Beklemede</span>',
                'order-status-processing' => '<span class="text-blue-500">İşleniyor</span>',
                'order-status-completed'  => '<span class="text-green-500">Tamamlandı</span>',
                'order-status-failed'     => '<span class="text-red-500">Başarısız</span>',
                'order-status-canceled'   => '<span class="text-red-500">İptal Edildi</span>',
                'order-date'              => 'Sipariş Tarihi:',
                'wallet'                  => 'Cüzdan',
                'wallet-after-completion' => '(Sipariş tamamlandıktan sonra)',
                'balance'                 => 'Bakiye:',
            ],
        ],
    ],
    'sell-gold' => [
        'summary' => [
            'page-title' => 'Altın Sat',
            'heading'    => 'Altın Sat',

            'select-payment-type'      => 'Ödeme Türü Seç',
            'select-address'           => 'Adres Seç',
            'select-chain'             => 'Blok Zinciri Seç',
            'select-chain-placeholder' => 'Lütfen bir blok zinciri seçin',
            'select-coin'              => 'Coin Seç',
            'select-coin-placeholder'  => 'Lütfen bir coin seçin',
            'continue'                 => 'Devam Et',
            'add-balance'              => 'Bakiye Ekle',
            'my-cart'                  => 'Sepetim (Borsa)',
            'total'                    => 'Toplam Tutar',
            'maximum-selling'          => 'Maksimum satış: <b>:max</b> adet',
        ],
        'authorization' => [
            'page-title' => 'Altın Sat',
            'heading'    => 'Altın Sat',

            'show-payment-details'  => 'Ödeme Detaylarını Göster',
            'chain'                 => 'Blok Zinciri',
            'wallet-balance'        => 'Cüzdan Bakiyesi',
            'processing-amount'     => 'İşlem Tutarı',
            'wallet-gas-fee'        => 'Cüzdan Gas Ücreti',
            'transaction-amount'    => 'İşlem Tutarı',
            'expected-fee'          => 'Beklenen Ücret',
            'expected-mirum-fee'    => 'MIRUM Ağ Ücreti',
            'risk-clauses'          => '<a class="text-terra-orange" id="privacy-policy">Gizlilik Politikasını</a> okudum ve kabul ediyorum',
            'terms-conditions'      => '<a class="text-terra-orange" id="terms-and-conditions">Şartlar ve Koşulları</a> okudum ve kabul ediyorum.',
            'data-protect'          => 'Satın aldığım RWA\'nın Mirum Network\'te saklanmasını okudum ve kabul ediyorum.',
            'cancellation-policy'   => '<a class="text-terra-orange" id="cancellation-policy">İptal Politikasını</a> okudum ve kabul ediyorum.',
            'pay-now'               => 'Ödeme Yap',
            'order-confirm'         => 'Siparişi Onayla',
            'cart'                  => 'Sepetim',
            'total'                 => 'Toplam Tutar',
            'continue'              => 'Devam Et',
            'cart_details'          => 'Sepet Detayları',
            'my_cart'               => 'Sepetim',
            'pending-balance-title' => 'Bekleyen Sipariş Ücretini Aktarın',
            'pending-balance-fail'  => 'Şu anda işlenmekte olan bekleyen ödeme taleplerimiz var. Lütfen tamamlanmasını bekleyin.',
            'my-cart'               => 'Sepetim (Borsa)',
            'exchange-note'         => 'Güncellemek istiyor musunuz? <a href=":url" class="font-bold text-[#D0AA49]">Geri dön</a>',
        ],
        'result' => [
            'completed' => [
                'page-title'  => 'Ödeme Tamamlandı',
                'heading'     => 'Ödeme Tamamlandı',
                'title'       => 'Ödeme Tamamlandı',
                'description' => 'Ödemeniz başarıyla tamamlandı. İşleminiz için teşekkür ederiz. <br> Siparişinizin detaylarını içeren bir e-posta göndereceğiz.',
                'my-orders'   => 'Siparişlerim',
            ],
            'failed' => [
                'page-title'  => 'Ödeme Başarısız',
                'heading'     => 'Ödeme Başarısız',
                'title'       => 'Ödeme Başarısız',
                'description' => 'Ödemeniz başarısız oldu. Lütfen ödeme bilgilerinizi kontrol edip tekrar deneyin. <br> Sorun devam ederse, lütfen bizimle iletişime geçin.',
                'my-orders'   => 'Siparişlerim',
            ],
            'pending' => [
                'page-title'  => 'Ödeme Bekleniyor',
                'heading'     => 'Ödeme Bekleniyor',
                'title'       => 'Ödeme Bekleniyor',
                'description' => 'Ödemeniz henüz doğrulanamadı, bu işlem arka planda devam ederken lütfen bekleyin. <br> İşleniyor ve kısa süre içinde tamamlanacak. Siparişinizin detaylarını içeren bir e-posta göndereceğiz.',
                'my-orders'   => 'Siparişlerim',
            ],
            'processing' => [
                'page-title'  => 'Ödeme İşleniyor',
                'heading'     => 'Ödeme İşleniyor',
                'title'       => 'Ödeme İşleniyor',
                'description' => 'Ödemeniz onaylandı ve satın aldığınız coinler en kısa sürede cüzdanınıza aktarılacak. <br> Siparişinizin detaylarını içeren bir e-posta göndereceğiz.',
                'my-orders'   => 'Siparişlerim',
            ],
        ],
    ],
    'pay-with-iyzico' => [
        'authorization' => [
            'page-title' => 'Kredi Kartı ile Ödeme',
            'heading'    => 'Güvenli Kredi Kartı Ödemesi',
        ],
        'result' => [
            'completed' => [
                'page-title'  => 'Ödeme Başarılı',
                'heading'     => 'Ödeme Tamamlandı',
                'title'       => 'Ödeme Başarılı',
                'description' => 'Ödemeniz başarıyla alındı. Sipariş faturanız e-posta adresinize gönderilecektir.<br><br>Siparişlerinizi "Siparişlerim" bölümünden takip edebilirsiniz.',
                'my-orders'   => 'Siparişlerimi Görüntüle',
            ],
            'failed' => [
                'page-title'  => 'Ödeme Başarısız',
                'heading'     => 'Ödeme Tamamlanamadı',
                'title'       => 'İşlem Başarısız',
                'description' => 'Ödemeniz gerçekleştirilemedi. Kart bilgilerinizi kontrol edip tekrar deneyin veya farklı bir ödeme yöntemi kullanın.<br><br>Sorun devam ederse, müşteri hizmetleriyle iletişime geçin.',
                'my-orders'   => 'Siparişlerimi Görüntüle',
            ],
        ],
    ],
    'pay-with-crypto' => [
        'summary' => [
            'page-title'               => 'Kripto ile Öde - Özet',
            'heading'                  => 'Kripto ile Öde',
            'select-payment-type'      => 'Ödeme Türünü Seçin',
            'select-address'           => 'Adres Seçin',
            'select-chain'             => 'Kripto Ağı Seçin',
            'select-chain-placeholder' => 'Lütfen bir ağ seçin',
            'select-coin'              => 'Kripto Para Seçin',
            'select-coin-placeholder'  => 'Lütfen bir kripto para seçin',
            'continue'                 => 'Devam Et',
            'add-balance'              => 'Bakiye Ekle',
            'my-cart'                  => 'Sepetim',
            'total'                    => 'Toplam Tutar',

            'deposit-with-crypto'             => 'Kripto ile Öde',
            'deposit-with-crypto-warning'     => 'Hesabınızda yeterli bakiye bulunmamaktadır. Siparişiniz için aşağıdaki cüzdan adresinize sipariş tutarı kadar kripto para aktarılması gerekmektedir.',
            'deposit-with-crypto-description' => 'Aşağıdaki Cüzdan\'a ödeme yapmalısınız',
        ],
        'authorization' => [
            'page-title'            => 'Kripto ile Öde - Onay',
            'heading'               => 'Kripto ile Öde',
            'show-payment-details'  => 'Ödeme Detaylarını Göster',
            'chain'                 => 'Kripto Ağı',
            'wallet-balance'        => 'Cüzdan Bakiyesi',
            'processing-amount'     => 'İşlenen Miktar',
            'wallet-gas-fee'        => 'Cüzdan Gas Ücreti',
            'transaction-amount'    => 'İşlem Miktarı',
            'expected-fee'          => 'Beklenen Ücret',
            'expected-mirum-fee'    => 'MIRUM Ağ Ücreti',
            'risk-clauses'          => '<a class="text-terra-orange" id="privacy-policy">Gizlilik Politikası</a>\'nı okudum ve kabul ediyorum',
            'terms-conditions'      => '<a class="text-terra-orange" id="terms-and-conditions">Şartlar ve Koşulları</a> okudum ve kabul ediyorum',
            'data-protection'       => '<a class="text-terra-orange" id="data-protection">Satın aldığım RWAların, Mirum Ağında depolanmasını</a> kabul ediyorum',
            'data-protect'          => '<a class="text-terra-orange" id="data-protect">Satın aldığım RWAların, Mirum Ağında depolanmasını</a> kabul ediyorum',
            'cancellation-policy'   => '<a class="text-terra-orange" id="cancellation-policy">İptal Koşulları\'nı</a> okudum ve kabul ediyorum.',
            'pay-now'               => 'Şimdi Öde',
            'order-confirm'         => 'Siparişi Tamamla',
            'cart'                  => 'Sepetim',
            'total'                 => 'Toplam Tutar',
            'continue'              => 'Devam Et',
            'cart_details'          => 'Sepet Detayları',
            'my_cart'               => 'Sepetim',
            'pending-balance-title' => 'Beklenen Sipariş Ücretini Aktarın',
            'pending-balance-fail'  => 'Şu anda işlenmekte olan bekleyen ödeme taleplerimiz var. Lütfen bunlar tamamlanana kadar bekleyin.',
        ],
        'result' => [
            'completed' => [
                'page-title'  => 'Ödeme Tamamlandı',
                'heading'     => 'Ödeme Tamamlandı',
                'title'       => 'Ödeme Tamamlandı',
                'description' => 'Ödemeniz başarıyla tamamlandı. İşlem için teşekkür ederiz. <br> Siparişinizin detaylarını içeren bir e-posta göndereceğiz.',
                'my-orders'   => 'Siparişlerim',
            ],
            'failed' => [
                'page-title'  => 'Ödeme Başarısız',
                'heading'     => 'Ödeme Başarısız',
                'title'       => 'Ödeme Başarısız',
                'description' => 'Ödemeniz başarısız oldu. Lütfen ödeme bilgilerinizi kontrol edin ve tekrar deneyin. <br> Sorun devam ederse lütfen bizimle iletişime geçin.',
                'my-orders'   => 'Siparişlerim',
            ],
            'pending' => [
                'page-title'  => 'Ödeme Bekleniyor',
                'heading'     => 'Ödeme Bekleniyor',
                'title'       => 'Ödeme Bekleniyor',
                'description' => 'Ödemeniz henüz doğrulanamadı, lütfen bu süreç arka planda devam ederken bekleyin. <br> İşleniyor ve kısa sürede tamamlanacak. Siparişinizin detaylarını içeren bir e-posta göndereceğiz.',
                'my-orders'   => 'Siparişlerim',
            ],
            'processing' => [
                'page-title'  => 'Ödeme İşleniyor',
                'heading'     => 'Ödeme İşleniyor',
                'title'       => 'Ödeme İşleniyor',
                'description' => 'Ödemeniz onaylandı ve satın alınan coinler mümkün olan en kısa sürede cüzdanınıza aktarılacaktır. <br> Siparişinizin detaylarını içeren bir e-posta göndereceğiz.',
                'my-orders'   => 'Siparişlerim',
            ],
        ],
    ],
    'moneytransfer' => [
        'authorization' => [
            'page-title'              => 'Para Transferi',
            'heading'                 => 'Para Transferi',
            'fee-note'                => 'Mirum Ağı, ağı üzerinden yapılan işlemler için işlem ücreti almaz.',
            'details'                 => 'Detaylar',
            'operation-fee'           => 'İşlem Ücreti',
            'network-fee'             => 'Ağ Ücreti',
            'grand-total'             => 'Genel Toplam',
            'company-confirm'         => 'Şirketi temsilen başvuruda bulunduğumu teyit eder, okudum ve kabul ediyorum.',
            'risk-clauses'            => '<a class="text-terra-orange" id="privacy-policy">Gizlilik Politikası</a>\'nı okudum ve kabul ediyorum.',
            'terms-conditions'        => '<a class="text-terra-orange" id="terms-and-conditions">Şartlar ve Koşulları</a> okudum ve kabul ediyorum.',
            'data-protect'            => 'Satın aldığım RWAların Mirum Ağında depolanmasını okudum ve kabul ediyorum.',
            'email-confirmation'      => 'Bilgilerin bana <span class="text-terra-orange">E-Posta</span> yoluyla gönderilmesini kabul ediyorum.',
            'personal-confirmation'   => 'Kendi adıma başvuruda bulunduğumu teyit eder, okudum ve kabul ediyorum.',
            'whitepaper-confirmation' => '<a class="text-terra-orange" id="whitepaper">Bilgi Notu</a>\'nu okudum ve belirtilen riskleri anladım.',
            'cancellation-policy'     => '<a class="text-terra-orange" id="cancellation-policy">İptal Koşulları</a>\'nı okudum ve kabul ediyorum.',
            'total'                   => 'Toplam Tutar',
            'pay-now'                 => 'Şimdi Öde',
            'order-confirm'           => 'Sipariş Ver',
            'vat'                     => 'KDV',
            'buy-button'              => 'SATIN AL',
            'continue'                => 'DEVAM ET',
            'money-transfer-fail'     => 'Para transferiyle verilen bekleyen bir siparişiniz var.<br />Siparişiniz tamamlanmadan ikinci bir para transferi siparişi veremezsiniz.<br />Lütfen siparişi iptal edin veya iptal için bizimle iletişime geçin.',
            'data-protection'         => '<a href="#" class="text-terra-orange" id="risk-clauses">Satın aldığım RWAların, Mirum Ağında depolanmasını</a> kabul ediyorum.',
        ],
        'success' => [
            'bank-info-not-exist' => [
                'heading'     => 'Banka Bilgilerine Erişilemedi',
                'description' => 'Ödemeniz başarıyla tamamlandı. İşlem için teşekkür ederiz. <br> Siparişinizin detaylarını içeren bir e-posta göndereceğiz.',
                'contact-us'  => 'Banka hesap bilgileri için lütfen bizimle iletişime geçin.',
            ],
        ],
    ],

    'home' => [
        'main' => [
            'browser_not_supported'  => 'Tarayıcınız video etiketini desteklemiyor.',
            'banner_title'           => 'Gayrimenkul Yatırımının Yeni Adı <span>Terramirum</span> ile:<br><span style="color: #12D176">Mirum Token</span>',
            'ready_to_sale'          => '1. Etap',
            'seed_sale'              => '1. Etap',
            'private_sale'           => '2. etap',
            'public_sale'            => '3. Etap',
            'metric_seed_sale'       => '1. Etap :date',
            'metric_private_sale'    => '2. etap :date',
            'metric_public_sale'     => '3. Etap :date',
            'coming_soon'            => 'Yakında',
            'private_sale_countdown' => [
                'styleText' => '2. etap',
                'noneText'  => 'başlayacak..',
            ],
            'public_sale_countdown' => [
                'styleText' => '3. Etap',
                'noneText'  => 'başlayacak..',
            ],
            // 'private_sale_countdown' => '<span style="color: #FF9700;">2. etap</span> Başlayacak..',
            'about_title'                          => 'Kripto dünyasında bir adım atmak için hazır mısınız?',
            'about_content'                        => 'ICO\'muz benzersiz bir fırsat sunuyor. Information Memorandum\'u inceleyin ve geleceği birlikte inşa etme şansını kaçırmayın!',
            'read_whitepaper'                      => 'Whitepaper\'ı Oku',
            'why_choose_us'                        => 'Bizi Neden Seçmelisiniz',
            'choose_our_token'                     => 'Neden Mirum Token\'ımızı Seçmelisiniz',
            'simplifying_payments'                 => 'Ödeme Sürecini Basitleştirme',
            'simplifying_payment_details'          => 'Ödeme sürecini basitleştirerek finansal kolaylık dünyasını açın, sorunsuz işlemler ve eşsiz kolaylıkla her etkileşimde eşi bulunmaz bir deneyim yaşayın.',
            'timeless_transactions'                => 'Zamansız işlemler.',
            'timeless_transaction_details'         => 'Her işlemin zamanın geçişine dayanacak şekilde özenle tasarlandığı bir finansal yolculuğa katılın, finansal çabalarınız için sorunsuz ve kalıcı bir miras sağlanır.',
            'secure_wealth'                        => 'Varlığınızı Güvence Altına Alın, Finansal Kontrolü Elde Tutun!',
            'secure_wealth_details'                => 'Varlıklı manzaranın üstesinden güvenle gelin - varlığınız güvence altında ve kontrol sizin elinizde!',
            'privacy_priority'                     => 'Kullanıcı gizliliğini sağlamak en önemli önceliğimizdir.',
            'privacy_priority_details'             => 'Güveniniz bizim için son derece önemlidir. Verilerinizin korunduğu ve çevrimiçi deneyiminizin endişesiz olduğu güvenli bir ortam yaratmaya adanmış olduğumuzu bilmenizi isteriz.',
            'mirum_chain_advantages'               => 'Mirum Protokolün Blokzincire Avantajları',
            'mirum_chain_advantages_details'       => 'Çeşitlilik ve Esneklik <br>IBC Etkileşimi<br>NFT Kiralama Fırsatları<br>PoS ile Staking ve Pasif Gelir<br>Topluluk Katılımı',
            'token_metrics'                        => 'Token Metrikleri',
            'seed_sale_date'                       => '1. Etap 14/10/2024',
            'seed_sale_amount'                     => ':amount Mirum',
            'private_sale_date'                    => '2. Etap 02/11/2024',
            'private_sale_amount'                  => ':amount Mirum',
            'public_sale_date'                     => '3. Etap 30/11/2024',
            'public_sale_amount'                   => ':amount Mirum',
            'seed_sale_tab'                        => '1. Etap',
            'private_sale_tab'                     => '2. etap',
            'public_sale_tab'                      => '3. Etap',
            'seed_sale_price'                      => '1 MIRUM = 0,0040 €',
            'seed_sale_info'                       => 'Toplam 125.000.000 MIRUM token, tohum turu satışı yoluyla satışa sunulacaktır. Bu tokenlar en avantajlı fiyat olan 0,0040 Eurodan satılacak ve bu tokenlar ICO sona erdikten 8 ay sonra kilidini açmaya başlayacaktır. Sonrasında, kilitler her ay %20 oranında açılacaktır.',
            'private_sale_price'                   => '1 MIRUM = 0,0072 €',
            'private_sale_info'                    => 'Toplam 208.333.333 MIRUM token, 2. etap sırasında satışa sunulacaktır. Bu tokenlar avantajlı fiyat olan 0,0072 Eurodan satılacak ve bu tokenlar ICO sona erdikten 6 ay sonra kilidini açmaya başlayacaktır. Sonrasında, kilitler her ay %10 oranında açılacaktır.',
            'public_sale_price'                    => '1 MIRUM = 0,0120 €',
            'public_sale_info'                     => 'Toplam 666.666.667 MIRUM token, 3. Etap sırasında satışa sunulacaktır. Bu tokenlar 0,0120 Eurodan satılacak ve bu tokenlar ICO sona erdikten 3 ay sonra kilidini açmaya başlayacaktır. Sonrasında, kilitler her ay %10 oranında açılacaktır.',
            'disclaimer'                           => '<span class="text-danger">*</span> Lütfen satın almadan önce Bilgi Notu\'nu okuyun.',
            'our_roadmap'                          => 'Yol Haritamız',
            'terramirum_strategy_and_project_plan' => 'Terramirum Stratejisi ve Proje Planı',
            'mid_of_q3_2021'                       => '2021 3. Çeyreğinin Ortası',
            //            'starting_point' => 'Başlangıç Noktası',
            'starting_point_description' => 'Pandemi ile ortaya çıkan konut sorununun büyük bir konut talebi patlamasına neden olduğu dönemdir. Bu bağlamda ortaya çıkan arzla ilgili sorunlar, özellikle hükümetler tarafından büyük endişeyle karşılanmış ve konut üretimini desteklemek için yeni likidite kaynakları oluşturulması için bankacılık sektöründen talepte bulunulmuştur. Gayrimenkul sektöründeki engeller belirlendi. Bu engellere çözüm bulmak için fikir aşaması başlamıştır.',
            //            'obstacles_identified' => 'Engeller Belirlendi',
            //            'idea_phase_begun' => 'Fikir Aşaması Başladı',
            'mid_of_q1_2022' => '2022 1. Çeyreğinin Ortası',
            //            'find_solutions_for_real_estate_sectors' => 'Gayrimenkul Sektörlerine Çözümler Bul',
            'find_solutions_for_real_estate_sectors_description' => 'Gerçekleştirilen roadmap, gayrimenkul sektörünün sorunlarına çözüm bulmayı planlamıştır. Konut üretimi ve tüketimiyle ilgili sorunları teknoloji desteğiyle çözmek için bir teknoloji platformunda bir araya gelecekleri planlandı. (Ürün geliştirme başlangıç fikir aşaması tamamlandı.) Web2 modellerinin temel tasarımı yapıldı. Şirket prosedürleri 2. aşamaya geçmek için tamamlanmıştır.',
            //            'web2_models_basic_design_completed' => 'Web2 Modelleri Temel Tasarımı Tamamlandı',
            //            'company_procedures_completed_for_stage_2' => '2. Aşama İçin Şirket Prosedürleri Tamamlandı',
            'mid_of_q2_2022' => '2022 2. Çeyreğinin Ortası',
            //            'basic_design_of_web3_models_was_done' => 'Web3 Modellerinin Temel Tasarımı Yapıldı',
            'web3_models_basic_design_completed' => 'Web3 Modelleri Temel Tasarımı Tamamlandı',
            //            'produce_nfts_and_sfts_for_real_estate_share_ownership' => 'Gayrimenkul Pay Sahipliği İçin NFT\'ler ve SFT\'ler Üret',
            //            'company_structure_expanded' => 'Şirket Yapısı Genişletildi',
            //            'website_design_started' => 'Web Sitesi Tasarımı Başladı',
            'mid_of_q1_2023' => '2023 1. Çeyreğinin Ortası',
            //            'cityfund_was_created_to_meet_tokenization_needs' => 'Tokenleştirme İhtiyaçlarını Karşılamak İçin Şehir Fonu Oluşturuldu',
            'support_agreements_aimed_with_key_companies' => 'Anahtar Şirketlerle Destek Anlaşmaları Hedeflendi',
            //            'negative_end_of_seed_investment_process' => 'Tohum Yatırım Sürecinin Negatif Sonu',
            //            'plan_for_terramirum_marketplace' => 'Terramirum Pazarı İçin Plan',
            //            'creation_of_urban_piggy_banks_for_investment' => 'Yatırım İçin Şehir Kümeleri Oluşturma',
            'mid_of_q2_2023' => '2023 2. Çeyreğinin Ortası',
            //            'advertising_activities_have_started' => 'Reklam Faaliyetleri Başladı',
            'focus_on_marketing_projects' => 'Pazarlama Projelerine Odaklanma',
            //            'carrying_out_market_activities' => 'Pazar Faaliyetlerinin Yürütülmesi',
            //            'expansion_of_advertising_activities' => 'Reklam Faaliyetlerinin Genişletilmesi',
            //            'continued_market_expansion' => 'Devam Eden Pazar Genişlemesi',
            //            'promotion_in_international_arena' => 'Uluslararası Arenada Tanıtım',
            //            'global_cooperation' => 'Küresel İşbirliği',
            //            'community_building_in_marketplace' => 'Pazar Yerinde Topluluk Oluşturma',
            'mid_of_q1_2024' => '2024 1. Çeyreğinin Ortası',
            //            'launchpad' => 'Başlangıç Platformu',
            'launchpad_setup_available_for_purchase' => 'Başlangıç Platformu Kurulumu Satın Alınabilir Durumda',
            //            'launching_applications_related_to_requirements' => 'Gereksinimlerle İlgili Uygulamaları Başlatma',
            //            'marketplace_dashboard_tests_continue' => 'Pazar Yeri Panosu Testleri Devam Ediyor',
            'mid_of_q2_2024_1' => '2024 2. Çeyreğinin Ortası',
            //            'activating_the_marketplace_dashboard' => 'Pazar Yeri Panosunu Aktive Etme',
            'promotional_activities_for_real_estate_investors' => 'Gayrimenkul Yatırımcıları için Tanıtım Faaliyetleri',
            //            'announcement_campaign_for_supplying_products_to_terramirum' => 'Ürünleri Terramirum\'a Tedarik İçin Duyuru Kampanyası',
            //            'interviews_with_various_media_organizations' => 'Çeşitli Medya Kuruluşlarıyla Röportajlar',
            'mid_of_q2_2024_2' => '2024 2. Çeyreğinin Ortası',
            //            'new_real_estate_based_models' => 'Yeni Gayrimenkul Tabanlı Modeller',
            'terra_mirum_chain_designed_and_services_put_into_service' => 'Başlangıç Platformu gelirlerinden sonra, Terramirum\'un kendi zinciri tasarlandı ve tüm hizmetleri bu zincir üzerinde hizmete sunuldu.',
            //            'creation_of_new_real_estate_based_income_models' => 'Yeni Gayrimenkul Tabanlı Gelir Modellerinin Oluşturulması',
            'q4_2024'        => '2024 4. Çeyreği',
            'q4_2024_detail' => 'Yeni işbirliklerinin oluşturulması. Kitle fonlaması (Crowdfunding) sürecinin başlaması. İkincil Market (Secondary Market) alyapı çalışmalarına başlanması.',
            'q1_2025'        => '2025 1. Çeyreği',
            'q2_2025'        => '2025 2. Çeyreği',
            'q3_2025'        => '2025 3. Çeyreği',
            'q1_2025_detail' => 'İkincil Market (Secondary Market) çalışmalarına devam edilmesi. Marketplace için çalışmaların başlaması. Marketplace gösterge tablosunun çeşitli veri setleriyle desteklenerek etkinleştirilmesi. Marketplace gösterge paneli testlerine devam edilmesi.',
            'q2_2025_detail' => 'İkincil Market (Secondary Market) çalışmalarına devam edilmesi. İşbirliklerinin oluşturulması. Reklam kampanyalarına devam edilmesi.',
            'q3_2025_detail' => 'İkincil Market (Secondary Market) çalışmalarının tamamlanarak faaliyete geçmesi.',
            'team_subtitle'  => 'Ekibimiz',
            'team_title'     => 'Liderlik Ekibi',
        ],
    ],

    'contact-us' => [
        'heading'     => 'Bize Ulaşın',
        'about-mirum' => [
            'title'       => 'Mirum Hakkında',
            'description' => 'MIRUM token, gayrimenkul satın alımı ve akıllı sözleşmeler için kullanılabilen bir fayda tokenı olarak sınıflandırılmaktadır. Credipto ekosisteminin likiditesini güvence altına almak amacıyla arz yüksek tutulmakta, bu sayede aşırı spekülasyon ve fiyat istikrarsızlığı önlenmektedir. Bu adım, ekosistemin sorunsuz gelişimini sağlamak adına gereklidir.',
        ],
        'contact-form' => [
            'title'     => 'Herhangi Bir Soru İçin Bize Ulaşın',
            'firstname' => 'Ad',
            'lastname'  => 'Soyad',
            'email'     => 'E-posta',
            'subject'   => 'Konu',
            'message'   => 'Mesaj',
            'send'      => 'Gönder',
        ],
    ],

    'footer' => [
        'home'           => 'Anasayfa',
        'about'          => 'Mirum Hakkında',
        'faq'            => 'SSS',
        'credipto'       => 'Credipto',
        'marketplace'    => 'Pazar Yeri',
        'contact'        => 'İletişim',
        'us'             => '',
        'footer-desc'    => 'Terramirum ile Gayrimenkul Yatırımının Yeni Adı: Dijital Değer!',
        'social-section' => [
            'title' => 'Sosyal',
        ],
        'quick-links-section' => [
            'title' => 'Hızlı Bağlantılar',
        ],
        'newsletter-section' => [
            'title' => 'Bülten',
        ],
        'copy-right' => 'Tüm Hakları Saklıdır © :date. <a href=":url" target="_blank">:name</a>',
        'contracts'  => 'Sözleşmeler',
    ],

    'mirum-coin' => [
        'per-price'            => 'Birim Fiyatı',
        'total-amount'         => 'Toplam Tutar',
        'buy-now'              => 'Şimdi Satın Al',
        'contract'             => 'Sözleşme',
        'contract-description' => 'Sözleşme Açıklaması',
        'contract-button'      => 'Sözleşmeyi Göster',
        'sold-meter'           => 'Satılan Miktar',
        'target-raised'        => 'Hedefe Ulaşıldı',
        'overlay-title'        => 'Mirum Token Satın Al',
        'overlay-description'  => '<p class="text-white">TerraMirum Platformunun ana hedefi, küçük bütçelerin yenilikçi teknolojilere yatırım yapmasını ve ev sahibi olmasını sağlayacak bir proje oluşturmaktır. Token satışından elde edilen gelir, projenin donanım ve operasyonel ihtiyaçlarını güvence altına almayı amaçlamaktadır. MIRUM tokeni, emlak ve akıllı sözleşmelerin satın alınmasında kullanılabilir, bu nedenle bir hizmet tokeni olarak sınıflandırılır. Credipto ekosisteminin likiditesini sağlamak amacıyla arz yüksek tutulur, böylece aşırı spekülasyon ve fiyat istikrarsızlığı önlenir, bu da MIRUM tokeninin fiyatında tehlikeli seviyelerde dalgalanmaya neden olabilir. Bu adım, ekosistemin kesintisiz gelişimini sağlamak için gereklidir.</p><h4 class="sub-title text-white">TerraMirum Proof of Stake (PoS) Kullanır</h4><p class="text-white">TerraMirum, kripto para birimi işlemlerini doğrulamak ve ağı güvence altına almak için Proof of Stake (PoS) modelini tercih eder. İşte bu modelin temel prensipleri ve avantajları:<br><br><b>Staking (Kilitleme):</b><br>PoS modelinde kullanıcılar, kripto paralarını stake eder. Bu, belirli bir süre için paralarını kilitledikleri ve ağın güvenliğine katkıda bulunmayı taahhüt ettikleri anlamına gelir. TerraMirum kullanıcıları, staking aracılığıyla paralarını erişilebilir kılar.<br><br><b>Doğrulama ve Blok İşlemleri:</b><br>Bir işlem bloğu işlenmeye hazır olduğunda, TerraMirum\'un PoS protokolü bir doğrulayıcı düğüm seçer. Bu doğrulayıcı, bloktaki işlemleri kontrol eder ve eğer doğrulanırsa, bloğu blok zincirine ekler. Doğrulayıcılar bu işlem için kripto ödülleri alır.<br><br><b>Stake Miktarına Göre Seçim:</b><br>Doğrulayıcı düğümlerin seçimi, stake edilen kripto para miktarına bağlıdır. Daha fazla kripto para stake eden katılımcılar, yeni blok ekleme olasılığı daha yüksek olanlardır. Bu, ağın güvenilirliğini ve ödül dağılımının adil olmasını sağlar.<br><br><b>Stake Havuzları:</b><br>Görece küçük miktarlarda stake eden kullanıcılar için doğrulayıcı olarak seçilme olasılığı düşük olabilir. Bu nedenle birçok kullanıcı, stake havuzlarına katılır. Havuzlar, kripto paraları birleştirerek daha büyük bir stake oluşturur ve ödülleri dağıtır.<br><br><b>Avantajlar:</b><br>TerraMirum\'un PoS modeli, enerji verimliliği, hızlı ve ekonomik işlemler ve özel ekipman ihtiyacı olmaması gibi avantajlar sunar. Ayrıca, madencilik işlemleri Mint olarak adlandırılır ve ödüller belirli enflasyon oranlarına göre belirlenir.<br><br><b>Temel Parametreler:</b><br>TerraMirum\'un PoS modelindeki temel parametreler arasında yıllık blok sayısı, hedef taahhüt oranı, maksimum enflasyon, minimum enflasyon, enflasyon oranı değişimi ve ödül değeri bulunur. Bu parametreler, ağın istikrarını ve ödül sistemini şekillendirmek için kullanılır.TerraMirum\'un PoS modeli, katılımcılarına yeşil, hızlı ve uygun maliyetli bir kripto para deneyimi sunmayı amaçlar.</p>',
    ],
    'company_register' => [
        'companyMail-email'                 => 'Lütfen geçerli bir e-mail adresi giriniz.',
        'companyMail-required'              => '1 Lütfen boş alanları doldurun.',
        'companyMail-unique'                => 'Girdiğiniz mail adresi kayıtlı',
        'companyName-required'              => '2 Lütfen boş alanları doldurun.',
        'companyPhone-string'               => 'Lütfen geçerli bir Telefon numarası giriniz.',
        'companyPhone-required'             => '3 Lütfen boş alanları doldurun.',
        'companyAddress-required'           => '4 Lütfen boş alanları doldurun.',
        'companyTaxNumber-string'           => 'Lütfen geçerli bir Tax numarası giriniz.',
        'companyTaxNumber-required'         => '5 Lütfen boş alanları doldurun.',
        'companyTaxNumber-min'              => 'Girdiğiniz değer min: {min} olmalıdır.',
        'companyRepresentative-required'    => '6 Lütfen boş alanları doldurun.',
        'legalType-required'                => 'Lütfen boş alanları doldurun.',
        'toast-popup-warning-title'         => 'Uyarı!',
        'toast-popup-warning-message'       => 'Lütfen Boş Alanları Doldurun.',
        'toast-popup-warning-email-message' => 'Lütfen geçerli bir mail adresi girin.',
    ],
];
