@extends('fast-order::layouts.miligold-default')

@section('page_title')
    {{ __('fast-order::app.ui.pages.summary.page-title') }}
@stop

@section('body')
    <main class="pt-12">
        <section class="relative bg-contain bg-right-top bg-no-repeat pt-28 pb-14">
            <div class="container relative z-10 text-center">
                <h1 class="font-display text-4xl lg:text-5xl font-bold text-gray-800">{{ __('fast-order::app.ui.pages.summary.title') }}</h1>
                <p class="mt-4 text-lg text-gray-600">{{ __('fast-order::app.ui.pages.summary.subtitle') }}</p>
            </div>
        </section>

        <section class="relative pb-12 dark:bg-jacarta-800">
            <picture class="pointer-events-none absolute inset-0 -z-10 hidden dark:block">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/gradient-inpage.webp"
                        type="image/webp">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png"
                        type="image/png">
                <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png"
                     class="h-full w-full">
            </picture>
            <picture class="pointer-events-none absolute inset-0 -z-10 dark:hidden">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/gradient-inpage.webp"
                        type="image/webp">
                <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png"
                        type="image/png">
                <img src="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/milyem/gradient-inpage.png"
                     class="h-full w-full">
            </picture>
            <div class="mx-auto max-w-5xl">
                @if ($errors->any())
                    <section class="relative">
                        <div class="container">
                            <div
                                class="relative mb-4 flex items-center justify-between w-full gap-2 p-4 bg-white text-red rounded-2xl border border-gray-200">
                                <div class="flex items-center justify-center gap-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                         stroke-width="1.5" stroke="currentColor" class="w-6 h-6 text-red-500">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                              d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z"/>
                                    </svg>
                                    <span class="flex-1">@foreach($errors->all() as $error)
                                            {{ $error }}
                                        @endforeach</span>
                                </div>
                            </div>
                        </div>
                    </section>
                @endif
                <fast_order></fast_order>
            </div>
        </section>
    </main>
@endsection

@push('scripts')
    <script type="text/x-template" id="fast_order_template">
        <div class="mx-auto max-w-5xl">
            <div class="space-y-6" v-if="isLoading">
                <div class="container">
                    <div class="flex flex-col-reverse md:flex-row gap-4">
                        <div
                            class="relative w-full rounded-2xl border border-gray-200 bg-white p-4 md:w-2/3 animate-pulse">
                            <div class="flex flex-col items-center gap-2 sm:flex-row mb-4">
                                <div class="bg-gray-200 border border-gray-200 rounded-xl h-28 flex-1"></div>
                                <div
                                    class="bg-gray-200 border border-gray-200 rounded-full h-12 w-12 mx-auto sm:mx-0"></div>
                                <div class="bg-gray-200 border border-gray-200 rounded-xl h-28 flex-1"></div>
                            </div>

                            <div class="border-t border-gray-200 space-y-4 pt-4 mb-4">
                                <div class="space-y-2">
                                    <div class="bg-gray-200 border border-gray-200 rounded-lg h-4 w-1/4"></div>
                                    <div class="bg-gray-200 border border-gray-200 rounded-lg h-12"></div>
                                </div>
                                <div class="space-y-2">
                                    <div class="bg-gray-200 border border-gray-200 rounded-lg h-4 w-1/4"></div>
                                    <div class="bg-gray-200 border border-gray-200 rounded-lg h-12"></div>
                                </div>
                                <div>
                                    <div class="bg-gray-200 border border-gray-200 rounded-lg h-12"></div>
                                </div>
                            </div>
                            <div class="border-t border-gray-200 pt-4">
                                <div class="bg-gray-200 rounded h-4 w-3/4 mx-auto"></div>
                            </div>
                        </div>

                        <div class="w-full md:w-1/3 space-y-4">
                            <div class="w-full p-4 bg-white rounded-2xl border border-gray-200 animate-pulse">
                                <div class="flex items-center justify-between gap-2">
                                    <div class="rounded-full bg-gray-200 h-10 w-10"></div>
                                    <div class="flex-1 space-y-3">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-2">
                                                <div class="h-6 bg-gray-200 rounded w-20"></div>
                                                <div class="h-5 bg-gray-200 rounded w-12"></div>
                                            </div>
                                            <div class="h-5 bg-gray-200 rounded-full w-16"></div>
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <div class="h-4 bg-gray-200 rounded w-24"></div>
                                            <div class="h-4 bg-gray-200 rounded w-16"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-3 pt-3 border-t border-gray-200 space-y-2">
                                    <div class="h-4 bg-gray-200 rounded w-3/4"></div>
                                    <div class="h-4 bg-gray-200 rounded w-1/2"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <section class="relative pb-12" v-else>
                <div class="container">
                    <div v-if="errorMessage"
                         class="relative mb-4 flex items-center justify-between w-full gap-2 p-4 bg-white text-red rounded-2xl border border-gray-200">
                        <div class="flex items-center justify-center gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                 stroke-width="1.5" stroke="currentColor" class="w-6 h-6 text-red-500">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                      d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z"/>
                            </svg>
                            <span class="flex-1">@{{ errorMessage }}</span>
                        </div>
                    </div>

                    <div class="flex flex-col-reverse gap-4 md:flex-row"
                         :class="{ 'opacity-25 filter grayscale pointer-events-none cursor-not-allowed': errorMessage }">
                        <div class="w-full md:w-2/3 relative p-4 bg-white rounded-2xl border border-gray-200">
                            <div class="flex flex-col gap-6">
                                <div v-if="transactionLocked" class="w-full">
                                    <div class="bg-light border-2 border-terra-gold rounded-xl p-4 mb-4">
                                        <div class="flex justify-between items-center mb-3">
                                            <h3 class="text-lg font-semibold text-gray-800">
                                                <span
                                                    v-if="selectedType === 'buy'">{{ __('fast-order::app.ui.pages.summary.locked.buy.confirmation.label') }}</span>
                                                <span
                                                    v-if="selectedType === 'sell'">{{ __('fast-order::app.ui.pages.summary.locked.sell.confirmation.label') }}</span>
                                            </h3>
                                            <span
                                                class="text-xs font-semibold text-white bg-terra-gold px-2 py-1 uppercase rounded-full">
                                                {{ __('fast-order::app.ui.pages.summary.locked.locked-price.label') }}
                                            </span>
                                        </div>

                                        <div class="space-y-3">
                                            <div class="flex justify-between text-sm text-gray-600">
                                                <span>{{ __('fast-order::app.ui.pages.summary.locked.transaction-type.label') }}</span>
                                                <span class="font-medium">
                                                    <span
                                                        v-if="selectedType === 'buy'">{{ __('fast-order::app.ui.pages.summary.buy-gold.label') }}</span>
                                                    <span
                                                        v-if="selectedType === 'sell'">{{ __('fast-order::app.ui.pages.summary.sell-gold.label') }}</span>
                                                </span>
                                            </div>
                                            <div class="flex justify-between text-sm text-gray-600">
                                                <span>{{ __('fast-order::app.ui.pages.summary.locked.gold-amount.label') }}</span>
                                                <span class="font-medium">@{{ goldAmount }} @{{ sellCurrency }}</span>
                                            </div>
                                            <div class="flex justify-between text-sm text-gray-600">
                                                <span>{{ __('fast-order::app.ui.pages.summary.locked.unit-price.label') }}</span>
                                                <span class="font-medium">@{{ lockedPrice }} @{{ currency }}</span>
                                            </div>
                                            <div class="flex justify-between text-sm text-gray-600">
                                                <span>{{ __('fast-order::app.ui.pages.summary.locked.total-amount.label') }}</span>
                                                <span class="font-medium">@{{ moneyAmount }} @{{ currency }}</span>
                                            </div>
                                            <div
                                                class="flex justify-between text-sm text-gray-600 border-t pt-2 mt-2 border-gray-200">
                                                <span>{{ __('fast-order::app.ui.pages.summary.locked.remaining-time.label') }}</span>
                                                <span class="font-medium">@{{ countdown }}@{{ countdown_after }}</span>
                                            </div>
                                        </div>

                                        <div class="mt-4 flex gap-2">
                                            <button
                                                v-if="transactionLocked"
                                                @click="cancelTransaction"
                                                class="flex-1 w-full py-3 px-4 rounded-lg bg-gray-300 text-gray-500 font-semibold transition-colors">
                                                {{ __('fast-order::app.ui.pages.summary.locked.button.cancel') }}
                                            </button>

                                            <button
                                                v-if="transactionLocked"
                                                @click="confirmTransaction"
                                                :disabled="!canConfirm"
                                                :class="['flex-1 w-full py-3 px-4 rounded-lg font-semibold transition-colors', confirmButtonClass]">
                                                {{ __('fast-order::app.ui.pages.summary.locked.button.confirm') }}
                                            </button>
                                            <button
                                                v-else
                                                @click="cancelTransaction"
                                                :disabled="!canConfirm"
                                                class="flex-1 w-full py-3 px-4 rounded-lg bg-gray-300 text-gray-500 font-semibold transition-colors">
                                                {{ __('fast-order::app.ui.pages.summary.locked.button.add-balance') }}
                                            </button>
                                        </div>
                                    </div>

                                    <div class="mt-3 pt-3 border-t border-gray-200">
                                        <p class="text-xs text-gray-400">
                                            {{ __('fast-order::app.ui.pages.summary.locked.locked-info') }}
                                        </p>
                                    </div>
                                </div>

                                <div v-if="!transactionLocked" class="flex flex-col sm:flex-row items-stretch gap-2">
                                    <div @click="selectedType = 'buy'"
                                         :class="{
                                            'border-terra-gold bg-white': selectedType === 'buy',
                                            'border-gray-200 bg-gray-50': selectedType !== 'buy'
                                         }"
                                         class="flex-1 flex flex-col items-center justify-center p-4 rounded-xl border-2 cursor-pointer transition-all">
                                        <span class="text-2xl font-semibold text-gray-700">
                                            {{ __('fast-order::app.ui.pages.summary.buy-gold.title') }}
                                        </span>
                                        <div
                                            class="mt-2 flex items-center space-x-1 border-t pt-2 px-2 border-gray-200">
                                            <span
                                                class="text-md font-bold text-terra-gold">@{{ prices.buy.amount }}</span>
                                            <span class="text-xs text-gray-500">@{{ prices.buy.currency }}</span>
                                        </div>
                                    </div>

                                    <button @click="selectedType = selectedType === 'buy' ? 'sell' : 'buy'"
                                            class="w-12 flex items-center justify-center self-center rounded-full h-12 transition hover:bg-gray-100">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                             stroke-width="1.5" stroke="currentColor"
                                             class="w-6 h-6 text-gray-600 hidden sm:block">
                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                  d="M7.5 21L3 16.5m0 0L7.5 12M3 16.5h13.5m0-13.5L21 7.5m0 0L16.5 12M21 7.5H7.5"/>
                                        </svg>

                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                             stroke-width="1.5" stroke="currentColor"
                                             class="w-6 h-6 text-gray-600 block sm:hidden">
                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                  d="M3 7.5 7.5 3m0 0L12 7.5M7.5 3v13.5m13.5 0L16.5 21m0 0L12 16.5m4.5 4.5V7.5"/>
                                        </svg>
                                    </button>

                                    <div @click="selectedType = 'sell'"
                                         :class="{
                                            'border-terra-gold bg-white': selectedType === 'sell',
                                            'border-gray-200 bg-gray-50': selectedType !== 'sell'
                                         }"
                                         class="flex-1 flex flex-col items-center justify-center p-4 rounded-xl border-2 cursor-pointer transition-all">
                                        <span class="text-2xl font-semibold text-gray-700">
                                            {{ __('fast-order::app.ui.pages.summary.sell-gold.title') }}
                                        </span>
                                        <div
                                            class="mt-2 flex items-center space-x-1 border-t pt-2 px-2 border-gray-200">
                                            <span
                                                class="text-md font-bold text-terra-gold">@{{ prices.sell.amount }}</span>
                                            <span class="text-xs text-gray-500">@{{ prices.sell.currency }}</span>
                                        </div>
                                    </div>
                                </div>

                                <div v-if="!transactionLocked" class="flex flex-col gap-4">
                                    <div class="space-y-2 border-t pt-4 border-gray-200">
                                        <label for="goldAmount" class="block text-sm font-medium text-gray-700">
                                            {{ __('fast-order::app.ui.pages.summary.form.gold-amount.label') }}
                                        </label>
                                        <div class="relative">
                                            <input id="goldAmount" type="text" v-model="goldAmount"
                                                   @input="onGoldInput"
                                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-terra-gold focus:border-terra-gold outline-none transition"
                                                   placeholder="{{ __('fast-order::app.ui.pages.summary.form.gold-amount.placeholder') }}">
                                            <span
                                                class="absolute right-3 top-3 text-gray-500">@{{ sellCurrency }}</span>
                                        </div>
                                    </div>

                                    <div class="space-y-2">
                                        <label for="moneyAmount" class="block text-sm font-medium text-gray-700">
                                            {{ __('fast-order::app.ui.pages.summary.form.money-amount.label') }}
                                        </label>
                                        <div class="relative">
                                            <input id="moneyAmount" type="text" v-model="moneyAmount"
                                                   @input="onMoneyInput"
                                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-terra-gold focus:border-terra-gold outline-none transition"
                                                   placeholder="{{ __('fast-order::app.ui.pages.summary.form.money-amount.placeholder') }}">
                                            <span class="absolute right-3 top-3 text-gray-500">@{{ currency }}</span>
                                        </div>
                                    </div>
                                </div>

                                <div v-if="!transactionLocked" class="text-center bg-white rounded-lg">
                                    <div v-if="!availableBalance && availableBalance !== null">
                                        <button
                                            class="w-full py-3 px-4 rounded-lg bg-gray-300 text-gray-500 font-semibold transition-colors">
                                            {{ __('fast-order::app.ui.pages.summary.form.button.insufficient-balance') }}
                                        </button>
                                    </div>
                                    <div v-else>
                                        <button @click="lockTransaction"
                                                :disabled="!goldAmount || goldAmount <= 0 && !moneyAmount || moneyAmount <= 0 && !availableBalance"
                                                :class="['w-full py-3 px-4 rounded-lg font-semibold transition-colors', lockButtonClass]">
                                            <span
                                                v-if="selectedType === 'buy'">{{ __('fast-order::app.ui.pages.summary.form.button.buy-gold') }}</span>
                                            <span
                                                v-if="selectedType === 'sell'">{{ __('fast-order::app.ui.pages.summary.form.button.sell-gold') }}</span>
                                        </button>
                                    </div>
                                </div>

                                <div v-if="!transactionLocked" class="pt-3 border-t border-gray-200">
                                    <div v-if="selectedType === 'buy'" class="text-center bg-white rounded-lg">
                                        <p class="text-xs text-gray-400">
                                            {!! __('fast-order::app.ui.pages.summary.buy-gold.limit-info', ['amount' => '@{{ balance[currency] || 0 }}', 'currency' => '@{{ currency }}', 'route' => route('customer.wallet.index')]) !!}
                                        </p>
                                    </div>

                                    <div v-if="selectedType === 'sell'" class="text-center bg-white rounded-lg">
                                        <p class="text-xs text-gray-400">
                                            {!! __('fast-order::app.ui.pages.summary.sell-gold.limit-info', ['amount' => '@{{ balance[sellCurrency] || 0 }}', 'currency' => '@{{ sellCurrency }}']) !!}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="w-full md:w-1/3 relative self-start space-y-4">
                            <div v-if="!transactionLocked"
                                 class="w-full p-4 bg-white rounded-2xl border border-gray-200">
                                <div class="flex items-center justify-between gap-2">
                                    <div class="rounded-full bg-gray-100 p-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                             stroke-width="1.5" stroke="currentColor"
                                             class="w-8 h-8 text-gray-500">
                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                  d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <div class="flex items-center justify-between gap-2 mt-1">
                                            <div class="text-sm text-gray-600 flex items-center gap-2">
                                                <span class="text-2xl font-bold text-gray-800">@{{ price }}</span>
                                                <span class="text-lg font-medium text-gray-500">@{{ currency }}</span>
                                            </div>
                                            <span
                                                class="text-xs font-semibold text-white bg-terra-gold px-2 py-1 uppercase rounded-full animate-pulse">
                                                {{ __('fast-order::app.ui.pages.summary.live.rate.label') }}
                                            </span>
                                        </div>
                                        <div class="text-sm text-gray-600 flex items-center">
                                            <span>{{ __('fast-order::app.ui.pages.summary.live.time-remaining.label') }}:</span>
                                            <span class="ml-1 font-medium text-gray-800"><b>@{{ countdown }}</b>@{{ countdown_after }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-3 pt-3 border-t border-gray-200">
                                    <p class="text-xs text-gray-400">
                                        {{ __('fast-order::app.ui.pages.summary.live.rate.update-info') }}
                                    </p>
                                </div>
                            </div>

                            <div v-if="transactionLocked"
                                 class="w-full p-4 bg-white rounded-2xl border border-gray-200">
                                <div class="flex items-center justify-between gap-2">
                                    <div class="rounded-full bg-gray-100 p-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                             stroke-width="1.5" stroke="currentColor"
                                             class="w-8 h-8 text-gray-500">
                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                  d="M16.5 10.5V6.75a4.5 4.5 0 1 0-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H6.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z"/>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <div class="flex items-center justify-between gap-2 mt-1">
                                            <div class="text-sm text-gray-600 flex items-center gap-2">
                                                <span class="text-2xl font-bold text-gray-800">@{{ countdown }}</span>
                                                <span
                                                    class="text-lg font-medium text-gray-500">@{{ countdown_after }}</span>
                                            </div>
                                            <span
                                                class="text-xs font-semibold text-white bg-terra-gold px-2 py-1 uppercase rounded-full animate-pulse">
                                                {{ __('fast-order::app.ui.pages.summary.live.locked-price.label') }}
                                            </span>
                                        </div>
                                        <div class="text-sm text-gray-600 flex items-center">
                                            <span>{{ __('fast-order::app.ui.pages.summary.live.unit-price.label') }}:</span>
                                            <span class="ml-1 font-medium text-gray-800"><b>@{{ price }}</b> @{{ currency }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-3 pt-3 border-t border-gray-200">
                                    <p class="text-xs text-gray-400">
                                        {{ __('fast-order::app.ui.pages.summary.live.rate.locked-info') }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form ref="paymentForm" method="POST" action="{{ route('fast-order.authorization') }}"
                          class="hidden">
                        @csrf
                        <input type="hidden" name="type" :value="selectedType">
                        <input type="hidden" name="quantity" :value="goldAmount">
                        <input type="hidden" name="price" :value="lockedPrice">
                        <input type="hidden" name="price_key" :value="priceKey">
                        <input type="hidden" name="product_id" value="{{ $product->id }}">
                    </form>
                </div>
            </section>
        </div>
    </script>
    <script>
        Vue.component('fast_order', {
            template: '#fast_order_template',
            data() {
                return {
                    types: ["buy", "sell"],
                    selectedType: "{{ $type }}",
                    priceFormat: 4,
                    price: 0,
                    priceKey: "",
                    currency: "",
                    sellCurrency: "{{ $product->sku }}",
                    prices: {
                        buy: {amount: 0, currency: ""},
                        sell: {amount: 0, currency: ""}
                    },
                    balance: [],
                    countdown: 60,
                    countdown_time: 60,
                    countdown_after: " {{ __('fast-order::app.ui.pages.summary.live.seconds') }}",
                    interval: null,
                    goldAmount: "{{ $quantity }}",
                    moneyAmount: "",
                    availableBalance: null,
                    transactionLocked: false,
                    lockedPrice: 0,
                    errorMessage: null,
                    lastEdited: null,
                    isLoading: true
                };
            },
            computed: {
                isTypeValid() {
                    return this.types.includes(this.selectedType);
                },
                hasValidAmount() {
                    const amt = this.selectedType === 'buy'
                        ? parseFloat(this.moneyAmount)
                        : parseFloat(this.goldAmount);
                    return !isNaN(amt) && amt > 0;
                },
                canLock() {
                    return this.isTypeValid &&
                        !this.transactionLocked &&
                        this.hasValidAmount &&
                        !this.errorMessage;
                },
                lockButtonClass() {
                    return this.canLock
                        ? 'bg-terra-gold hover:bg-terra-gold-dark text-white'
                        : 'bg-gray-300 text-gray-500 cursor-not-allowed';
                },
                canConfirm() {
                    return this.transactionLocked &&
                        this.availableBalance &&
                        this.isTypeValid &&
                        this.hasValidAmount;
                },
                confirmButtonClass() {
                    return this.canConfirm
                        ? 'bg-terra-gold hover:bg-terra-gold-dark text-white'
                        : 'bg-gray-300 text-gray-500 cursor-not-allowed';
                }
            },
            watch: {
                selectedType: async function (val) {
                    if (!this.isTypeValid) {
                        console.warn('Invalid type, resetting to default');
                        this.selectedType = this.types[0];
                    }
                    await this.refreshPrice();
                    this.availableBalance = this.checkBalance();
                },
                price(val) {
                    if (this.transactionLocked) return;
                    if (this.lastEdited === 'money') {
                        this.calculateGoldAmount();
                    } else {
                        this.calculateMoneyAmount();
                    }
                },
                goldAmount(val) {
                    if (this.selectedType === 'sell' && parseFloat(val) < 0) {
                        console.warn('Gold amount cannot be negative, resetting');
                        this.goldAmount = '';
                    }
                },
                moneyAmount(val) {
                    if (this.selectedType === 'buy' && parseFloat(val) < 0) {
                        console.warn('Money amount cannot be negative, resetting');
                        this.moneyAmount = '';
                    }
                }
            },
            async mounted() {
                await this.getBalance();
                await this.refreshPrice();
                this.availableBalance = this.checkBalance();
                this.isLoading = false;
            },
            methods: {
                onGoldInput() {
                    this.lastEdited = 'gold';
                    this.calculateMoneyAmount();
                },
                onMoneyInput() {
                    this.lastEdited = 'money';
                    this.calculateGoldAmount();
                },
                startCountdown() {
                    if (this.interval) clearInterval(this.interval);
                    this.interval = setInterval(() => {
                        if (this.errorMessage || this.countdown <= 0) {
                            clearInterval(this.interval);
                            if (!this.errorMessage) {
                                this.transactionLocked
                                    ? this.cancelTransaction()
                                    : this.refreshPrice();
                            }
                            return;
                        }
                        this.countdown--;
                    }, 1000);
                },
                async getBalance() {
                    try {
                        const response = await axios.get('{{ route("fast-order.balance") }}');
                        const data = response.data;
                        if (!data.success) {
                            this.errorMessage = data.message || 'Balance fetch failed';
                            return;
                        }
                        this.balance = data.data;
                        this.availableBalance = this.checkBalance();
                    } catch (err) {
                        this.errorMessage = err.response?.data?.message || err.message;
                        console.error('Balance fetch error:', err);
                    }
                },
                checkBalance() {
                    if (this.selectedType === 'buy') {
                        const required = parseFloat(this.moneyAmount || 0);
                        const avail = parseFloat(this.balance[this.currency] || 0);
                        return avail >= required;
                    } else {
                        const gold = parseFloat(this.goldAmount || 0);
                        const availGold = parseFloat(this.balance[this.sellCurrency] || 0);
                        return gold <= availGold;
                    }
                },
                async getPrices(lock = false) {
                    this.isLoading = false;
                    try {
                        const params = lock ? {lock: this.selectedType} : {};
                        const response = await axios.get('{{ route("fast-order.prices") }}', {params});
                        if (!response.data.success) {
                            this.errorMessage = response.data.message || 'Price fetch failed';
                            return;
                        }

                        const {buy, sell} = response.data.data;
                        this.prices.buy = {
                            amount: buy.price,
                            currency: buy.currency.code,
                            price_key: buy.price_key || null,
                        };
                        this.prices.sell = {
                            amount: sell.price,
                            currency: sell.currency.code,
                            price_key: sell.price_key || null,
                        };

                        const selected = this.prices[this.selectedType];
                        this.price = selected.amount;
                        this.currency = selected.currency;
                        if (lock) {
                            this.priceKey = selected.price_key;
                        }
                        this.errorMessage = null;
                    } catch (error) {
                        this.errorMessage = error.response?.data?.message || error.message || 'Price fetch failed';
                    } finally {
                        this.isLoading = false;
                    }
                },
                async refreshPrice() {
                    await this.getPrices();
                    clearInterval(this.interval);
                    this.countdown = this.countdown_time;
                    this.startCountdown();
                },
                calculateMoneyAmount() {
                    const rate = parseFloat(this.transactionLocked ? this.lockedPrice : this.price) || 0;
                    this.moneyAmount = this.goldAmount && rate
                        ? (parseFloat(this.goldAmount) * rate).toFixed(this.priceFormat)
                        : "";
                    this.availableBalance = this.checkBalance();
                },
                calculateGoldAmount() {
                    const rate = parseFloat(this.transactionLocked ? this.lockedPrice : this.price) || 0;
                    this.goldAmount = this.moneyAmount && rate
                        ? (parseFloat(this.moneyAmount) / rate).toFixed(this.priceFormat)
                        : "";
                    this.availableBalance = this.checkBalance();
                },
                async lockTransaction() {
                    if (!this.canLock) {
                        console.warn('Cannot lock transaction - validation failed');
                        return;
                    }
                    clearInterval(this.interval);
                    await this.getPrices(true);
                    if (this.lastEdited === 'money') {
                        this.calculateGoldAmount();
                    } else {
                        this.calculateMoneyAmount();
                    }
                    this.availableBalance = this.checkBalance();
                    this.lockedPrice = this.price;
                    this.transactionLocked = true;
                    this.countdown = this.countdown_time;
                    this.errorMessage = null;
                    this.startCountdown();
                },
                cancelTransaction() {
                    this.transactionLocked = false;
                    this.lockedPrice = 0;
                    clearInterval(this.interval);
                    this.countdown = this.countdown_time;
                    this.startCountdown();
                },
                confirmTransaction() {
                    if (!this.canConfirm) {
                        console.warn('Cannot confirm transaction - validation failed');
                        return;
                    }

                    this.$refs.paymentForm.submit();
                },
            },
            beforeUnmount() {
                clearInterval(this.interval);
            }
        });
    </script>
@endpush
