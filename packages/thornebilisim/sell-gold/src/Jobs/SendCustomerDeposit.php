<?php

namespace Thorne\SellGold\Jobs;

use App\Services\OAuth\ApiClientAuth10Service;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SendCustomerDeposit implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 5;

    public array $backoff = [15, 30, 60, 120, 240];

    public int $timeout = 300;

    private ApiClientAuth10Service $apiService;

    private $order;

    public function __construct($order, ApiClientAuth10Service $apiService)
    {
        $this->order      = $order;
        $this->apiService = $apiService;
    }

    public function handle()
    {
        if (in_array($this->order->status, ['completed', 'failed', 'pending'])) {
            return;
        }
        if ($this->order->status !== 'processing') {
            Log::channel('sell-gold')->warning('SendCustomerDeposit için order durumu uygun değil', [
                'order_id'       => $this->order->id,
                'current_status' => $this->order->status,
            ]);

            return;
        }

        try {
            $amount   = $this->order->meta()->where('meta_key', 'txAmount')->first()?->value()   ?? 0;
            $currency = $this->order->meta()->where('meta_key', 'txCurrency')->first()?->value() ?? 'EURM';
            $chain    = $this->order->meta()->where('meta_key', 'txChain')->first()?->value()    ?? 'mirum-testnet';

            $requestData = [
                'tenantId'  => 50,
                'userId'    => $this->order->customer_id,
                'payeeType' => 'CRYPTO',
                'amount'    => $amount * pow(10, 6),
                'exponent'  => 6,
                'currency'  => $currency,
                'chainId'   => $chain,
            ];

            Log::channel('sell-gold')->info('SendCustomerDeposit deposit isteği gönderiliyor', [
                'order_id'     => $this->order->id,
                'request_data' => $requestData,
            ]);

            $response = $this->apiService->baseUrl2('/wallet/deposit')
                ->baseMethod('post')
                ->baseClient($requestData);

            if ($response->successful() && $response->json('isSuccessful') === 5) {
                DB::transaction(function () {
                    $this->order->status = 'completed';
                    $this->order->save();

                    dispatch(new ProcessCustomerDeposit($this->order))->onQueue('miligram');
                });

                Log::channel('sell-gold')->info('SendCustomerDeposit başarıyla tamamlandı', [
                    'order_id' => $this->order->id,
                ]);
            } else {
                throw new Exception('Deposit işlemi başarısız oldu: '.json_encode($response->json()));
            }
        } catch (Exception $exception) {
            Log::channel('sell-gold')->warning('SendCustomerDeposit başarısız oldu', [
                'order_id'  => $this->order->id,
                'attempts'  => $this->attempts(),
                'exception' => $exception->getMessage(),
            ]);

            if ($this->attempts() >= $this->tries) {
                DB::transaction(function () {
                    $this->order->status = 'failed';
                    $this->order->save();
                    dispatch(new ProcessAdminDeposit($this->order))->onQueue('miligram');
                });
            }

            throw $exception;
        }
    }

    public function failed(Exception $exception)
    {
        Log::channel('sell-gold')->error('SendCustomerDeposit tamamen başarısız oldu', [
            'order_id'  => $this->order->id,
            'attempts'  => $this->attempts(),
            'exception' => $exception->getMessage(),
        ]);

        DB::transaction(function () {
            $this->order->status = 'failed';
            $this->order->save();
        });
    }
}
